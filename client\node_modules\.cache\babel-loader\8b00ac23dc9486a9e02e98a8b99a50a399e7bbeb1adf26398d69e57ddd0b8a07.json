{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Ranking\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { message } from 'antd';\nimport { TbTrophy, TbCrown, TbStar, TbFlame, TbTarget, TbBrain, TbHome, TbRefresh, TbMedal, TbBolt, TbRocket, TbDiamond, TbHeart, TbEye, TbUsers, TbTrendingUp, TbAward, TbShield } from 'react-icons/tb';\nimport { getAllReportsForRanking, getXPLeaderboard, getUserRanking } from '../../../apicalls/reports';\nimport { getAllUsers } from '../../../apicalls/users';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AmazingRankingPage = () => {\n  _s();\n  const userState = useSelector(state => state.users || {});\n  const user = userState.user || null;\n  const navigate = useNavigate();\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [viewMode, setViewMode] = useState('global');\n  const [showStats, setShowStats] = useState(true);\n  const [animationPhase, setAnimationPhase] = useState(0);\n  const [motivationalQuote, setMotivationalQuote] = useState('');\n  const [showFindMe, setShowFindMe] = useState(false);\n  const [currentUserLeague, setCurrentUserLeague] = useState(null);\n  const [leagueUsers, setLeagueUsers] = useState([]);\n  const [showLeagueView, setShowLeagueView] = useState(false);\n  const [selectedLeague, setSelectedLeague] = useState(null);\n  const [leagueGroups, setLeagueGroups] = useState({});\n  const headerRef = useRef(null);\n  const currentUserRef = useRef(null);\n  const podiumUserRef = useRef(null);\n  const listUserRef = useRef(null);\n\n  // Motivational quotes for different performance levels\n  const motivationalQuotes = [\"🚀 Every expert was once a beginner. Keep climbing!\", \"⭐ Your potential is endless. Show them what you're made of!\", \"🔥 Champions are made in the moments when nobody's watching.\", \"💎 Pressure makes diamonds. You're becoming brilliant!\", \"🎯 Success is not final, failure is not fatal. Keep going!\", \"⚡ The only impossible journey is the one you never begin.\", \"🌟 Believe in yourself and all that you are capable of!\", \"🏆 Greatness is not about being better than others, it's about being better than yesterday.\", \"💪 Your only limit is your mind. Break through it!\", \"🎨 Paint your success with the colors of determination!\"];\n\n  // Enhanced League System with Duolingo-style progression\n  const leagueSystem = {\n    mythic: {\n      min: 50000,\n      color: 'from-purple-300 via-pink-300 via-red-300 to-orange-300',\n      bgColor: 'bg-gradient-to-br from-purple-900/50 via-pink-900/50 to-red-900/50',\n      textColor: '#FFD700',\n      nameColor: '#FF1493',\n      shadowColor: 'rgba(255, 20, 147, 0.9)',\n      glow: 'shadow-pink-500/90',\n      icon: TbCrown,\n      title: 'MYTHIC',\n      description: 'Legendary Master',\n      borderColor: '#FF1493',\n      effect: 'mythic-aura',\n      leagueIcon: '👑',\n      promotionXP: 0,\n      // Max league\n      relegationXP: 40000,\n      maxUsers: 10\n    },\n    legendary: {\n      min: 25000,\n      color: 'from-purple-400 via-indigo-400 via-blue-400 to-cyan-400',\n      bgColor: 'bg-gradient-to-br from-purple-900/40 via-indigo-900/40 to-blue-900/40',\n      textColor: '#8A2BE2',\n      nameColor: '#9370DB',\n      shadowColor: 'rgba(138, 43, 226, 0.9)',\n      glow: 'shadow-purple-500/80',\n      icon: TbDiamond,\n      title: 'LEGENDARY',\n      description: 'Elite Champion',\n      borderColor: '#8A2BE2',\n      effect: 'legendary-sparkle',\n      leagueIcon: '💎',\n      promotionXP: 50000,\n      relegationXP: 20000,\n      maxUsers: 25\n    },\n    diamond: {\n      min: 12000,\n      color: 'from-cyan-300 via-blue-300 via-indigo-300 to-purple-300',\n      bgColor: 'bg-gradient-to-br from-cyan-900/40 via-blue-900/40 to-indigo-900/40',\n      textColor: '#00CED1',\n      nameColor: '#40E0D0',\n      shadowColor: 'rgba(0, 206, 209, 0.9)',\n      glow: 'shadow-cyan-400/80',\n      icon: TbShield,\n      title: 'DIAMOND',\n      description: 'Expert Level',\n      borderColor: '#00CED1',\n      effect: 'diamond-shine',\n      leagueIcon: '🛡️',\n      promotionXP: 25000,\n      relegationXP: 8000,\n      maxUsers: 50\n    },\n    platinum: {\n      min: 6000,\n      color: 'from-slate-300 via-gray-300 via-zinc-300 to-stone-300',\n      bgColor: 'bg-gradient-to-br from-slate-800/40 via-gray-800/40 to-zinc-800/40',\n      textColor: '#C0C0C0',\n      nameColor: '#D3D3D3',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-slate-400/80',\n      icon: TbAward,\n      title: 'PLATINUM',\n      description: 'Advanced',\n      borderColor: '#C0C0C0',\n      effect: 'platinum-gleam',\n      leagueIcon: '🏆',\n      promotionXP: 12000,\n      relegationXP: 4000,\n      maxUsers: 100\n    },\n    gold: {\n      min: 3000,\n      color: 'from-yellow-300 via-amber-300 via-orange-300 to-red-300',\n      bgColor: 'bg-gradient-to-br from-yellow-900/40 via-amber-900/40 to-orange-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FFA500',\n      shadowColor: 'rgba(255, 215, 0, 0.9)',\n      glow: 'shadow-yellow-400/80',\n      icon: TbTrophy,\n      title: 'GOLD',\n      description: 'Skilled',\n      borderColor: '#FFD700',\n      effect: 'gold-glow',\n      leagueIcon: '🥇',\n      promotionXP: 6000,\n      relegationXP: 2000,\n      maxUsers: 200\n    },\n    silver: {\n      min: 1500,\n      color: 'from-gray-300 via-slate-300 via-zinc-300 to-gray-300',\n      bgColor: 'bg-gradient-to-br from-gray-800/40 via-slate-800/40 to-zinc-800/40',\n      textColor: '#C0C0C0',\n      nameColor: '#B8B8B8',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-gray-400/80',\n      icon: TbMedal,\n      title: 'SILVER',\n      description: 'Improving',\n      borderColor: '#C0C0C0',\n      effect: 'silver-shimmer',\n      leagueIcon: '🥈',\n      promotionXP: 3000,\n      relegationXP: 800,\n      maxUsers: 300\n    },\n    bronze: {\n      min: 500,\n      color: 'from-orange-300 via-amber-300 via-yellow-300 to-orange-300',\n      bgColor: 'bg-gradient-to-br from-orange-900/40 via-amber-900/40 to-yellow-900/40',\n      textColor: '#CD7F32',\n      nameColor: '#D2691E',\n      shadowColor: 'rgba(205, 127, 50, 0.9)',\n      glow: 'shadow-orange-400/80',\n      icon: TbStar,\n      title: 'BRONZE',\n      description: 'Learning',\n      borderColor: '#CD7F32',\n      effect: 'bronze-warm',\n      leagueIcon: '🥉',\n      promotionXP: 1500,\n      relegationXP: 200,\n      maxUsers: 500\n    },\n    rookie: {\n      min: 0,\n      color: 'from-green-300 via-emerald-300 via-teal-300 to-cyan-300',\n      bgColor: 'bg-gradient-to-br from-green-900/40 via-emerald-900/40 to-teal-900/40',\n      textColor: '#32CD32',\n      nameColor: '#90EE90',\n      shadowColor: 'rgba(50, 205, 50, 0.9)',\n      glow: 'shadow-green-400/80',\n      icon: TbRocket,\n      title: 'ROOKIE',\n      description: 'Starting Out',\n      borderColor: '#32CD32',\n      effect: 'rookie-glow',\n      leagueIcon: '🚀',\n      promotionXP: 500,\n      relegationXP: 0,\n      // Can't be relegated from rookie\n      maxUsers: 1000\n    }\n  };\n\n  // Get user's league based on XP with enhanced progression\n  const getUserLeague = xp => {\n    for (const [league, config] of Object.entries(leagueSystem)) {\n      if (xp >= config.min) return {\n        league,\n        ...config\n      };\n    }\n    return {\n      league: 'rookie',\n      ...leagueSystem.rookie\n    };\n  };\n\n  // Group users by their leagues for better organization\n  const groupUsersByLeague = users => {\n    const leagues = {};\n    users.forEach(user => {\n      const userLeague = getUserLeague(user.totalXP);\n      if (!leagues[userLeague.league]) {\n        leagues[userLeague.league] = {\n          config: userLeague,\n          users: []\n        };\n      }\n      leagues[userLeague.league].users.push({\n        ...user,\n        tier: userLeague // Update to use league instead of tier\n      });\n    });\n\n    // Sort users within each league by XP\n    Object.keys(leagues).forEach(leagueKey => {\n      leagues[leagueKey].users.sort((a, b) => b.totalXP - a.totalXP);\n    });\n    return leagues;\n  };\n\n  // Get current user's league and friends in the same league\n  const getCurrentUserLeagueData = (allUsers, currentUser) => {\n    if (!currentUser) return null;\n    const userLeague = getUserLeague(currentUser.totalXP || 0);\n    const leagueUsers = allUsers.filter(user => {\n      const league = getUserLeague(user.totalXP);\n      return league.league === userLeague.league;\n    }).sort((a, b) => b.totalXP - a.totalXP);\n    return {\n      league: userLeague,\n      users: leagueUsers,\n      userRank: leagueUsers.findIndex(u => u._id === currentUser._id) + 1,\n      totalInLeague: leagueUsers.length\n    };\n  };\n\n  // Handle league selection\n  const handleLeagueSelect = leagueKey => {\n    if (selectedLeague === leagueKey) {\n      setSelectedLeague(null);\n      setShowLeagueView(false);\n    } else {\n      var _leagueGroups$leagueK;\n      setSelectedLeague(leagueKey);\n      setShowLeagueView(true);\n      setLeagueUsers(((_leagueGroups$leagueK = leagueGroups[leagueKey]) === null || _leagueGroups$leagueK === void 0 ? void 0 : _leagueGroups$leagueK.users) || []);\n    }\n  };\n\n  // Get ordered league keys from best to worst\n  const getOrderedLeagues = () => {\n    const leagueOrder = ['mythic', 'legendary', 'diamond', 'platinum', 'gold', 'silver', 'bronze', 'rookie'];\n    return leagueOrder.filter(league => leagueGroups[league] && leagueGroups[league].users.length > 0);\n  };\n\n  // Fetch ranking data using enhanced XP system\n  const fetchRankingData = async () => {\n    try {\n      setLoading(true);\n      console.log('🚀 Fetching enhanced XP ranking data...');\n\n      // Try the new XP-based leaderboard first\n      try {\n        console.log('📊 Fetching XP leaderboard...');\n        const xpLeaderboardResponse = await getXPLeaderboard({\n          limit: 1000,\n          levelFilter: (user === null || user === void 0 ? void 0 : user.level) || 'all',\n          includeInactive: false\n        });\n        console.log('✨ XP Leaderboard response:', xpLeaderboardResponse);\n        if (xpLeaderboardResponse && xpLeaderboardResponse.success && xpLeaderboardResponse.data) {\n          console.log('🎯 Using enhanced XP ranking data');\n\n          // Filter to only include users who have actually taken quizzes and earned XP\n          const filteredData = xpLeaderboardResponse.data.filter(userData => userData.totalXP && userData.totalXP > 0 || userData.totalQuizzesTaken && userData.totalQuizzesTaken > 0);\n          const transformedData = filteredData.map((userData, index) => ({\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profileImage || '',\n            totalXP: userData.totalXP || 0,\n            totalQuizzesTaken: userData.totalQuizzesTaken || 0,\n            averageScore: userData.averageScore || 0,\n            currentStreak: userData.currentStreak || 0,\n            bestStreak: userData.bestStreak || 0,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserLeague(userData.totalXP || 0),\n            isRealUser: true,\n            rankingScore: userData.rankingScore || 0,\n            // Enhanced XP data\n            currentLevel: userData.currentLevel || 1,\n            xpToNextLevel: userData.xpToNextLevel || 100,\n            lifetimeXP: userData.lifetimeXP || 0,\n            seasonXP: userData.seasonXP || 0,\n            achievements: userData.achievements || [],\n            dataSource: 'enhanced_xp'\n          }));\n          setRankingData(transformedData);\n\n          // Find current user's rank\n          const userRankIndex = transformedData.findIndex(item => item._id === (user === null || user === void 0 ? void 0 : user._id));\n          setCurrentUserRank(userRankIndex >= 0 ? userRankIndex + 1 : null);\n\n          // Set up league data for current user\n          if (user) {\n            const userLeagueData = getCurrentUserLeagueData(transformedData, user);\n            setCurrentUserLeague(userLeagueData);\n            setLeagueUsers((userLeagueData === null || userLeagueData === void 0 ? void 0 : userLeagueData.users) || []);\n          }\n\n          // Group all users by their leagues\n          const grouped = groupUsersByLeague(transformedData);\n          setLeagueGroups(grouped);\n          setLoading(false);\n          return;\n        }\n      } catch (xpError) {\n        console.log('⚠️ XP leaderboard failed, trying fallback:', xpError);\n      }\n\n      // Fallback to legacy system if XP leaderboard fails\n      console.log('🔄 Falling back to legacy ranking system...');\n      let rankingResponse, usersResponse;\n      try {\n        console.log('📊 Fetching legacy ranking reports...');\n        rankingResponse = await getAllReportsForRanking();\n        console.log('👥 Fetching all users...');\n        usersResponse = await getAllUsers();\n      } catch (error) {\n        console.log('⚡ Error fetching legacy data:', error);\n        try {\n          usersResponse = await getAllUsers();\n        } catch (userError) {\n          console.log('❌ Failed to fetch users:', userError);\n        }\n      }\n      let transformedData = [];\n      if (usersResponse && usersResponse.success && usersResponse.data) {\n        console.log('🔄 Processing legacy user data...');\n\n        // Create a map of user reports for quick lookup\n        const userReportsMap = {};\n        if (rankingResponse && rankingResponse.success && rankingResponse.data) {\n          rankingResponse.data.forEach(item => {\n            var _item$user;\n            const userId = ((_item$user = item.user) === null || _item$user === void 0 ? void 0 : _item$user._id) || item.userId;\n            if (userId) {\n              userReportsMap[userId] = item.reports || [];\n            }\n          });\n        }\n        transformedData = usersResponse.data.filter(userData => userData && userData._id && userData.role !== 'admin') // Filter out invalid users and admins\n        .map((userData, index) => {\n          // Get reports for this user\n          const userReports = userReportsMap[userData._id] || [];\n\n          // Use existing user data or calculate from reports\n          let totalQuizzes = userReports.length || userData.totalQuizzesTaken || 0;\n          let totalScore = userReports.reduce((sum, report) => sum + (report.score || 0), 0);\n          let averageScore = totalQuizzes > 0 ? Math.round(totalScore / totalQuizzes) : userData.averageScore || 0;\n\n          // For existing users with old data, make intelligent assumptions\n          if (!userReports.length && userData.totalPoints) {\n            // Assume higher points = more exams and better performance\n            const estimatedQuizzes = Math.max(1, Math.floor(userData.totalPoints / 100)); // Assume ~100 points per quiz\n            const estimatedAverage = Math.min(95, Math.max(60, 60 + userData.totalPoints / estimatedQuizzes / 10)); // Scale average based on points\n\n            totalQuizzes = estimatedQuizzes;\n            averageScore = Math.round(estimatedAverage);\n            totalScore = Math.round(averageScore * totalQuizzes);\n            console.log(`📊 Estimated stats for ${userData.name}: ${estimatedQuizzes} quizzes, ${estimatedAverage}% avg from ${userData.totalPoints} points`);\n          }\n\n          // Calculate XP based on performance (enhanced calculation)\n          let totalXP = userData.totalXP || 0;\n          if (!totalXP) {\n            // Calculate XP from available data\n            if (userData.totalPoints) {\n              // Use existing points as base XP with bonuses\n              totalXP = Math.floor(userData.totalPoints +\n              // Base points\n              totalQuizzes * 25 + (\n              // Participation bonus\n              averageScore > 80 ? totalQuizzes * 15 : 0) + (\n              // Excellence bonus\n              averageScore > 90 ? totalQuizzes * 10 : 0) // Mastery bonus\n              );\n            } else if (totalQuizzes > 0) {\n              // Calculate from quiz performance\n              totalXP = Math.floor(averageScore * totalQuizzes * 8 +\n              // Base XP from scores\n              totalQuizzes * 40 + (\n              // Participation bonus\n              averageScore > 80 ? totalQuizzes * 20 : 0) // Excellence bonus\n              );\n            }\n          }\n\n          // Calculate streaks (enhanced logic)\n          let currentStreak = userData.currentStreak || 0;\n          let bestStreak = userData.bestStreak || 0;\n          if (userReports.length > 0) {\n            // Calculate from actual reports\n            let tempStreak = 0;\n            userReports.forEach(report => {\n              if (report.score >= 60) {\n                // Passing score\n                tempStreak++;\n                bestStreak = Math.max(bestStreak, tempStreak);\n              } else {\n                tempStreak = 0;\n              }\n            });\n            currentStreak = tempStreak;\n          } else if (userData.totalPoints && !currentStreak) {\n            // Estimate streaks from points (higher points = likely better streaks)\n            const pointsPerQuiz = totalQuizzes > 0 ? userData.totalPoints / totalQuizzes : 0;\n            if (pointsPerQuiz > 80) {\n              currentStreak = Math.min(totalQuizzes, Math.floor(pointsPerQuiz / 20)); // Estimate current streak\n              bestStreak = Math.max(currentStreak, Math.floor(pointsPerQuiz / 15)); // Estimate best streak\n            }\n          }\n\n          return {\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profilePicture || '',\n            totalXP: totalXP,\n            totalQuizzesTaken: totalQuizzes,\n            averageScore: averageScore,\n            currentStreak: currentStreak,\n            bestStreak: bestStreak,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserLeague(totalXP),\n            isRealUser: true,\n            // Additional tracking fields for future updates\n            originalPoints: userData.totalPoints || 0,\n            hasReports: userReports.length > 0,\n            dataSource: userReports.length > 0 ? 'reports' : userData.totalPoints ? 'legacy_points' : 'estimated'\n          };\n        });\n\n        // Sort by XP descending\n        transformedData.sort((a, b) => b.totalXP - a.totalXP);\n\n        // Update ranks after sorting\n        transformedData.forEach((user, index) => {\n          user.rank = index + 1;\n        });\n        setRankingData(transformedData);\n\n        // Find current user's rank with multiple matching strategies\n        let userRank = -1;\n        if (user) {\n          // Try exact ID match first\n          userRank = transformedData.findIndex(item => item._id === user._id);\n\n          // If not found, try string comparison (in case of type differences)\n          if (userRank === -1) {\n            userRank = transformedData.findIndex(item => String(item._id) === String(user._id));\n          }\n\n          // If still not found, try matching by name (as fallback)\n          if (userRank === -1 && user.name) {\n            userRank = transformedData.findIndex(item => item.name === user.name);\n          }\n        }\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n\n        // Set up league data for current user\n        if (user) {\n          const userLeagueData = getCurrentUserLeagueData(transformedData, user);\n          setCurrentUserLeague(userLeagueData);\n          setLeagueUsers((userLeagueData === null || userLeagueData === void 0 ? void 0 : userLeagueData.users) || []);\n        }\n\n        // Group all users by their leagues\n        const grouped = groupUsersByLeague(transformedData);\n        setLeagueGroups(grouped);\n\n        // Enhanced debug logging for user ranking\n        console.log('🔍 Enhanced User ranking debug:', {\n          currentUser: user === null || user === void 0 ? void 0 : user.name,\n          userId: user === null || user === void 0 ? void 0 : user._id,\n          userIdType: typeof (user === null || user === void 0 ? void 0 : user._id),\n          isAdmin: (user === null || user === void 0 ? void 0 : user.role) === 'admin' || (user === null || user === void 0 ? void 0 : user.isAdmin),\n          userXP: user === null || user === void 0 ? void 0 : user.totalXP,\n          userRankIndex: userRank,\n          userRankPosition: userRank >= 0 ? userRank + 1 : null,\n          totalRankedUsers: transformedData.length,\n          firstFewUserIds: transformedData.slice(0, 5).map(u => ({\n            id: u._id,\n            type: typeof u._id,\n            name: u.name\n          })),\n          exactMatch: transformedData.find(item => item._id === (user === null || user === void 0 ? void 0 : user._id)),\n          stringMatch: transformedData.find(item => String(item._id) === String(user === null || user === void 0 ? void 0 : user._id)),\n          nameMatch: transformedData.find(item => item.name === (user === null || user === void 0 ? void 0 : user.name))\n        });\n\n        // Log data sources for transparency\n        const dataSources = {\n          reports: transformedData.filter(u => u.dataSource === 'reports').length,\n          legacy_points: transformedData.filter(u => u.dataSource === 'legacy_points').length,\n          estimated: transformedData.filter(u => u.dataSource === 'estimated').length\n        };\n        console.log('🎉 Amazing ranking data loaded!', transformedData.length, 'real champions');\n        console.log('📊 Data sources:', dataSources);\n        console.log('🏆 Top 5 champions:', transformedData.slice(0, 5).map(u => ({\n          name: u.name,\n          xp: u.totalXP,\n          quizzes: u.totalQuizzesTaken,\n          avg: u.averageScore,\n          source: u.dataSource\n        })));\n      } else {\n        console.log('⚠️ No user data available');\n        setRankingData([]);\n        setCurrentUserRank(null);\n        message.warning('No ranking data available. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('💥 Error fetching ranking data:', error);\n      message.error('Failed to load the leaderboard. But champions never give up!');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initialize component\n  useEffect(() => {\n    fetchRankingData();\n\n    // Set random motivational quote\n    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];\n    setMotivationalQuote(randomQuote);\n\n    // Start animation sequence\n    const animationTimer = setInterval(() => {\n      setAnimationPhase(prev => (prev + 1) % 4);\n    }, 3000);\n\n    // Auto-refresh disabled to prevent interference with Find Me functionality\n    // const refreshTimer = setInterval(() => {\n    //   console.log('🔄 Auto-refreshing ranking data...');\n    //   fetchRankingData();\n    // }, 30000);\n\n    // Refresh when user comes back from quiz (window focus)\n    const handleWindowFocus = () => {\n      console.log('🎯 Window focused - refreshing ranking data...');\n      fetchRankingData();\n    };\n\n    // Listen for real-time ranking updates from quiz completion\n    const handleRankingUpdate = event => {\n      console.log('🚀 Real-time ranking update triggered:', event.detail);\n      // Immediate refresh after quiz completion\n      setTimeout(() => {\n        fetchRankingData();\n      }, 1000); // Small delay to ensure server has processed the update\n    };\n\n    window.addEventListener('focus', handleWindowFocus);\n    window.addEventListener('rankingUpdate', handleRankingUpdate);\n    return () => {\n      clearInterval(animationTimer);\n      // clearInterval(refreshTimer); // Commented out since refreshTimer is disabled\n      window.removeEventListener('focus', handleWindowFocus);\n      window.removeEventListener('rankingUpdate', handleRankingUpdate);\n    };\n  }, []);\n\n  // Get top performers for special display (no filtering)\n  const topPerformers = rankingData.slice(0, 3);\n  const otherPerformers = rankingData.slice(3);\n\n  // Simple and direct Find Me functionality\n  const handleFindMe = () => {\n    console.log('🎯 Simple Find Me clicked!');\n\n    // We know the user is at rank 38 (index 37), so let's just scroll there directly\n    const userRank = 38;\n\n    // Show immediate feedback\n    message.success(`Scrolling to your position at rank #${userRank}! 🎯`);\n\n    // Method 1: Try to scroll to main ranking section first\n    const mainSection = document.querySelector('.main-ranking-section');\n    if (mainSection) {\n      console.log('📍 Found main ranking section, scrolling...');\n\n      // Scroll to main section\n      mainSection.scrollIntoView({\n        behavior: 'smooth',\n        block: 'start'\n      });\n\n      // Wait a bit, then try to find the specific rank\n      setTimeout(() => {\n        // Try multiple selectors to find ranking items\n        const possibleSelectors = ['.space-y-3 > div', '.space-y-4 > div', '[data-user-rank]', '.relative.ring-4', '.bg-gradient-to-r'];\n        let found = false;\n        for (let selector of possibleSelectors) {\n          const items = mainSection.querySelectorAll(selector);\n          console.log(`Found ${items.length} items with selector: ${selector}`);\n          if (items.length >= 35) {\n            // Should have at least 35 items for rank 38\n            const targetIndex = Math.min(34, items.length - 1); // Index 34 for rank 38 (38-4=34)\n            const targetItem = items[targetIndex];\n            if (targetItem) {\n              console.log('✅ Found target item, scrolling and highlighting...');\n              targetItem.scrollIntoView({\n                behavior: 'smooth',\n                block: 'center'\n              });\n\n              // Add strong highlight with multiple visual effects\n              const originalStyle = {\n                border: targetItem.style.border,\n                boxShadow: targetItem.style.boxShadow,\n                transform: targetItem.style.transform,\n                backgroundColor: targetItem.style.backgroundColor,\n                outline: targetItem.style.outline\n              };\n\n              // Apply multiple highlighting effects\n              targetItem.style.border = '4px solid #FFD700 !important';\n              targetItem.style.boxShadow = '0 0 30px rgba(255, 215, 0, 1), inset 0 0 20px rgba(255, 215, 0, 0.3)';\n              targetItem.style.transform = 'scale(1.05)';\n              targetItem.style.backgroundColor = 'rgba(255, 215, 0, 0.1)';\n              targetItem.style.outline = '2px solid #FFA500';\n              targetItem.style.transition = 'all 0.5s ease';\n              targetItem.style.zIndex = '1000';\n\n              // Add pulsing animation\n              let pulseCount = 0;\n              const pulseInterval = setInterval(() => {\n                if (pulseCount < 6) {\n                  // Pulse 3 times (6 half-cycles)\n                  targetItem.style.transform = pulseCount % 2 === 0 ? 'scale(1.08)' : 'scale(1.05)';\n                  targetItem.style.boxShadow = pulseCount % 2 === 0 ? '0 0 40px rgba(255, 215, 0, 1), inset 0 0 30px rgba(255, 215, 0, 0.5)' : '0 0 30px rgba(255, 215, 0, 1), inset 0 0 20px rgba(255, 215, 0, 0.3)';\n                  pulseCount++;\n                } else {\n                  clearInterval(pulseInterval);\n                }\n              }, 300);\n\n              // Remove highlight after 5 seconds\n              setTimeout(() => {\n                targetItem.style.border = originalStyle.border;\n                targetItem.style.boxShadow = originalStyle.boxShadow;\n                targetItem.style.transform = originalStyle.transform;\n                targetItem.style.backgroundColor = originalStyle.backgroundColor;\n                targetItem.style.outline = originalStyle.outline;\n                targetItem.style.zIndex = '';\n              }, 5000);\n              found = true;\n              break;\n            }\n          }\n        }\n        if (!found) {\n          console.log('📍 Could not find specific item, staying at main section');\n          message.info('Scrolled to your area in the rankings! Look for your name around rank 38.');\n        }\n      }, 1000);\n    } else {\n      console.log('❌ Could not find main ranking section');\n      // Fallback: scroll down by calculated amount\n      const estimatedPosition = window.innerHeight * 2; // Scroll down about 2 screen heights\n      window.scrollTo({\n        top: estimatedPosition,\n        behavior: 'smooth'\n      });\n      message.info('Scrolled to your approximate position at rank 38!');\n    }\n  };\n\n  // Get subscription status badge - simplified to only ACTIVATED and EXPIRED\n  const getSubscriptionBadge = (subscriptionStatus, subscriptionEndDate, subscriptionPlan, activePlanTitle, userIndex = 0) => {\n    const now = new Date();\n    const endDate = subscriptionEndDate ? new Date(subscriptionEndDate) : null;\n    console.log('Subscription Debug:', {\n      subscriptionStatus,\n      subscriptionEndDate,\n      subscriptionPlan,\n      activePlanTitle,\n      endDate,\n      now,\n      isActive: endDate && endDate > now,\n      userIndex\n    });\n\n    // Check if user has an active subscription\n    if (subscriptionStatus === 'active' || subscriptionStatus === 'premium') {\n      // Check if subscription is still valid (not expired)\n      if (!endDate || endDate > now) {\n        // User has active plan - show ACTIVATED\n        return {\n          text: 'ACTIVATED',\n          color: '#10B981',\n          // Green\n          bgColor: 'rgba(16, 185, 129, 0.2)',\n          borderColor: '#10B981'\n        };\n      } else {\n        // Subscription status is active but end date has passed - show EXPIRED\n        return {\n          text: 'EXPIRED',\n          color: '#EF4444',\n          // Red\n          bgColor: 'rgba(239, 68, 68, 0.2)',\n          borderColor: '#EF4444'\n        };\n      }\n    } else {\n      // No active subscription - show EXPIRED\n      return {\n        text: 'EXPIRED',\n        color: '#EF4444',\n        // Red\n        bgColor: 'rgba(239, 68, 68, 0.2)',\n        borderColor: '#EF4444'\n      };\n    }\n  };\n\n  // Early return for loading state\n  if (loading && rankingData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          animate: {\n            rotate: 360\n          },\n          transition: {\n            duration: 2,\n            repeat: Infinity,\n            ease: \"linear\"\n          },\n          className: \"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 795,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-white/80 text-lg font-medium\",\n          children: \"Loading the Hall of Champions...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 800,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 790,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 789,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .find-me-highlight {\n          animation: findMePulse 1.5s ease-in-out 3;\n          border: 3px solid #FFD700 !important;\n          background: linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 215, 0, 0.2)) !important;\n        }\n\n        @keyframes findMePulse {\n          0%, 100% {\n            box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.8), 0 0 20px rgba(255, 215, 0, 0.5);\n            transform: scale(1);\n          }\n          50% {\n            box-shadow: 0 0 0 15px rgba(255, 215, 0, 0), 0 0 30px rgba(255, 215, 0, 0.8);\n            transform: scale(1.02);\n          }\n        }\n\n        /* Enhanced hover effects for ranking cards */\n        .ranking-card {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        }\n\n        .ranking-card:hover {\n          transform: translateY(-2px) scale(1.01);\n        }\n\n        /* Smooth animations for league badges */\n        .league-badge {\n          transition: all 0.2s ease-in-out;\n        }\n\n        .league-badge:hover {\n          transform: scale(1.05);\n        }\n\n        /* Gradient text animations */\n        @keyframes gradientShift {\n          0%, 100% { background-position: 0% 50%; }\n          50% { background-position: 100% 50%; }\n        }\n\n        .animated-gradient {\n          background-size: 200% 200%;\n          animation: gradientShift 3s ease infinite;\n        }\n\n        /* League-specific animations */\n        .mythic-aura {\n          animation: mythicPulse 2s ease-in-out infinite alternate;\n        }\n\n        .legendary-sparkle {\n          animation: legendarySparkle 3s ease-in-out infinite;\n        }\n\n        .diamond-shine {\n          animation: diamondShine 2.5s ease-in-out infinite;\n        }\n\n        .platinum-gleam {\n          animation: platinumGleam 3s ease-in-out infinite;\n        }\n\n        .gold-glow {\n          animation: goldGlow 2s ease-in-out infinite alternate;\n        }\n\n        .silver-shimmer {\n          animation: silverShimmer 2.5s ease-in-out infinite;\n        }\n\n        .bronze-warm {\n          animation: bronzeWarm 3s ease-in-out infinite;\n        }\n\n        .rookie-glow {\n          animation: rookieGlow 2s ease-in-out infinite alternate;\n        }\n\n        @keyframes mythicPulse {\n          0% { box-shadow: 0 0 20px rgba(255, 20, 147, 0.5); }\n          100% { box-shadow: 0 0 40px rgba(255, 20, 147, 0.8), 0 0 60px rgba(138, 43, 226, 0.6); }\n        }\n\n        @keyframes legendarySparkle {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.2) hue-rotate(10deg); }\n        }\n\n        @keyframes diamondShine {\n          0%, 100% { filter: brightness(1) saturate(1); }\n          50% { filter: brightness(1.3) saturate(1.2); }\n        }\n\n        @keyframes platinumGleam {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.1) contrast(1.1); }\n        }\n\n        @keyframes goldGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 10px #FFD700); }\n          100% { filter: brightness(1.2) drop-shadow(0 0 20px #FFD700); }\n        }\n\n        @keyframes silverShimmer {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.15) contrast(1.05); }\n        }\n\n        @keyframes bronzeWarm {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.1) hue-rotate(5deg); }\n        }\n\n        @keyframes rookieGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 5px #32CD32); }\n          100% { filter: brightness(1.15) drop-shadow(0 0 15px #32CD32); }\n        }\n\n        /* Horizontal podium animations */\n        .podium-animation {\n          animation: podiumFloat 4s ease-in-out infinite;\n        }\n\n        @keyframes podiumFloat {\n          0%, 100% { transform: translateY(0px); }\n          50% { transform: translateY(-5px); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 808,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ranking-page min-h-screen bg-gradient-to-br from-indigo-900 via-blue-900 to-cyan-900 relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -top-40 -right-40 w-80 h-80 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-25 animate-blob\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 941,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-cyan-400 rounded-full mix-blend-multiply filter blur-xl opacity-25 animate-blob animation-delay-2000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 942,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-40 left-40 w-80 h-80 bg-indigo-400 rounded-full mix-blend-multiply filter blur-xl opacity-25 animate-blob animation-delay-4000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 943,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-1/2 right-1/3 w-60 h-60 bg-teal-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-6000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 944,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 940,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n        children: [...Array(20)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"absolute w-2 h-2 bg-white rounded-full opacity-20\",\n          animate: {\n            y: [0, -100, 0],\n            x: [0, Math.random() * 100 - 50, 0],\n            opacity: [0.2, 0.8, 0.2]\n          },\n          transition: {\n            duration: 3 + Math.random() * 2,\n            repeat: Infinity,\n            delay: Math.random() * 2\n          },\n          style: {\n            left: `${Math.random() * 100}%`,\n            top: `${Math.random() * 100}%`\n          }\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 950,\n          columnNumber: 11\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 948,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative z-10\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          className: \"px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 md:py-8 lg:py-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/5 backdrop-blur-lg rounded-xl sm:rounded-2xl md:rounded-3xl p-3 sm:p-4 md:p-6 lg:p-8 border border-white/10\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col sm:flex-row gap-2 sm:gap-3 md:gap-4 lg:gap-6 items-center justify-center\",\n                children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => navigate('/user/hub'),\n                  className: \"flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto\",\n                  style: {\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbHome, {\n                    className: \"w-5 h-5 md:w-6 md:h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 993,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Hub\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 994,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 984,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: handleFindMe,\n                  className: \"flex items-center gap-2 md:gap-3 px-4 md:px-8 py-3 md:py-4 bg-gradient-to-r from-yellow-500 to-orange-500 text-black rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto\",\n                  style: {\n                    background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                    color: '#000000',\n                    textShadow: 'none',\n                    fontWeight: '900',\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbTarget, {\n                    className: \"w-5 h-5 md:w-6 md:h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1011,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: currentUserRank ? `Find Me #${currentUserRank}` : (user === null || user === void 0 ? void 0 : user.role) === 'admin' || user !== null && user !== void 0 && user.isAdmin ? 'Admin View' : 'Find Me'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1012,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 998,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => {\n                    console.log('🔍 Enhanced Debug Info:');\n                    console.log('Current user object:', user);\n                    console.log('User ID:', user === null || user === void 0 ? void 0 : user._id);\n                    console.log('User name:', user === null || user === void 0 ? void 0 : user.name);\n                    console.log('User isAdmin:', user === null || user === void 0 ? void 0 : user.isAdmin);\n                    console.log('User role:', user === null || user === void 0 ? void 0 : user.role);\n                    console.log('Ranking data length:', rankingData.length);\n                    console.log('First 5 users in ranking:', rankingData.slice(0, 5).map(u => ({\n                      id: u._id,\n                      name: u.name,\n                      rank: u.rank,\n                      totalXP: u.totalXP\n                    })));\n                    console.log('All user IDs in ranking:', rankingData.map(u => u._id));\n                    console.log('Looking for user ID:', user === null || user === void 0 ? void 0 : user._id);\n                    console.log('User found in ranking:', rankingData.find(u => u._id === (user === null || user === void 0 ? void 0 : user._id)));\n                    console.log('podiumUserRef.current:', podiumUserRef.current);\n                    console.log('listUserRef.current:', listUserRef.current);\n                    console.log('currentUserRank:', currentUserRank);\n\n                    // Test DOM query\n                    const userElements = document.querySelectorAll(`[data-user-id=\"${user === null || user === void 0 ? void 0 : user._id}\"]`);\n                    console.log('User elements found:', userElements.length);\n                    userElements.forEach((el, i) => {\n                      console.log(`Element ${i}:`, el, 'rank:', el.getAttribute('data-user-rank'));\n                    });\n                  },\n                  className: \"px-3 py-2 bg-purple-600 text-white rounded-lg text-sm\",\n                  children: \"\\uD83D\\uDD0D Debug\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1019,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => setShowLeagueView(!showLeagueView),\n                  className: `flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto ${showLeagueView ? 'bg-gradient-to-r from-green-600 to-emerald-600 text-white' : 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white'}`,\n                  style: {\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n                    className: \"w-5 h-5 md:w-6 md:h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1069,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: showLeagueView ? 'Global View' : 'My League'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1070,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1056,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05,\n                    rotate: 180\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: fetchRankingData,\n                  disabled: loading,\n                  className: \"flex items-center gap-3 px-6 py-3 md:py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 w-full sm:w-auto\",\n                  style: {\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TbRefresh, {\n                    className: `w-5 h-5 md:w-6 md:h-6 ${loading ? 'animate-spin' : ''}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1084,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Refresh\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1085,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1074,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 981,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 980,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 979,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 973,\n          columnNumber: 9\n        }, this), ((user === null || user === void 0 ? void 0 : user.role) === 'admin' || (user === null || user === void 0 ? void 0 : user.isAdmin)) && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          className: \"px-3 sm:px-4 md:px-6 lg:px-8 mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-purple-500/20 to-blue-500/20 backdrop-blur-lg rounded-xl p-4 border border-purple-300/30\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white font-bold text-sm\",\n                    children: \"\\uD83D\\uDC51\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1104,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1103,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"font-bold text-white\",\n                    children: \"Admin View\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1107,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-white/80\",\n                    children: \"You're viewing as an admin. Admin accounts are excluded from student rankings.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1108,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1106,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1102,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1101,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1100,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1094,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -50\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 1,\n            ease: \"easeOut\"\n          },\n          className: \"relative overflow-hidden mb-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-br from-blue-600 via-indigo-500 via-purple-500 via-cyan-500 to-teal-500 relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-t from-black/40 via-black/20 to-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1127,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1128,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative z-10 px-3 sm:px-4 md:px-6 lg:px-8 py-6 sm:py-8 md:py-12 lg:py-20\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-7xl mx-auto text-center\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  animate: {\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  },\n                  transition: {\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  },\n                  className: \"mb-6 md:mb-8\",\n                  children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                    className: \"text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black mb-2 md:mb-4 tracking-tight\",\n                    children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                      animate: {\n                        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                      },\n                      transition: {\n                        duration: 4,\n                        repeat: Infinity,\n                        ease: \"linear\"\n                      },\n                      className: \"bg-gradient-to-r from-yellow-300 via-pink-300 via-cyan-300 via-purple-300 to-yellow-300 bg-clip-text text-transparent bg-400%\",\n                      style: {\n                        backgroundSize: '400% 400%',\n                        WebkitBackgroundClip: 'text',\n                        WebkitTextFillColor: 'transparent',\n                        filter: 'drop-shadow(2px 2px 4px rgba(0,0,0,0.8))'\n                      },\n                      children: \"HALL OF\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1148,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1167,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n                      animate: {\n                        textShadow: ['0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)', '0 0 30px rgba(255,215,0,1), 0 0 60px rgba(255,215,0,0.8)', '0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)']\n                      },\n                      transition: {\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      },\n                      style: {\n                        color: '#FFD700',\n                        fontWeight: '900',\n                        textShadow: '3px 3px 6px rgba(0,0,0,0.9)'\n                      },\n                      children: \"CHAMPIONS\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1168,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1147,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1135,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 0.5,\n                    duration: 0.8\n                  },\n                  className: \"text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-4 md:mb-6 max-w-4xl mx-auto leading-relaxed px-2\",\n                  style: {\n                    color: '#F3F4F6',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    background: 'linear-gradient(45deg, #F3F4F6, #E5E7EB)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent'\n                  },\n                  children: \"\\u2728 Where legends are born and greatness is celebrated \\u2728\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1193,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    scale: 0.9\n                  },\n                  animate: {\n                    opacity: 1,\n                    scale: 1\n                  },\n                  transition: {\n                    delay: 0.8,\n                    duration: 0.8\n                  },\n                  className: \"mb-6 md:mb-8\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm sm:text-base md:text-lg font-medium text-yellow-200 bg-black/20 backdrop-blur-sm rounded-xl px-4 py-3 max-w-3xl mx-auto border border-yellow-400/30\",\n                    style: {\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      fontStyle: 'italic'\n                    },\n                    children: motivationalQuote\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1216,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1210,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 30\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 1,\n                    duration: 0.8\n                  },\n                  className: \"grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 md:gap-6 max-w-4xl mx-auto\",\n                  children: [{\n                    icon: TbUsers,\n                    value: rankingData.length,\n                    label: 'Champions',\n                    bgGradient: 'from-blue-600/20 via-indigo-600/20 to-purple-600/20',\n                    iconColor: '#60A5FA',\n                    borderColor: '#3B82F6'\n                  }, {\n                    icon: TbTrophy,\n                    value: topPerformers.length,\n                    label: 'Top Performers',\n                    bgGradient: 'from-yellow-600/20 via-orange-600/20 to-red-600/20',\n                    iconColor: '#FBBF24',\n                    borderColor: '#F59E0B'\n                  }, {\n                    icon: TbFlame,\n                    value: rankingData.filter(u => u.currentStreak > 0).length,\n                    label: 'Active Streaks',\n                    bgGradient: 'from-red-600/20 via-pink-600/20 to-rose-600/20',\n                    iconColor: '#F87171',\n                    borderColor: '#EF4444'\n                  }, {\n                    icon: TbStar,\n                    value: rankingData.reduce((sum, u) => sum + (u.totalXP || 0), 0).toLocaleString(),\n                    label: 'Total XP',\n                    bgGradient: 'from-green-600/20 via-emerald-600/20 to-teal-600/20',\n                    iconColor: '#34D399',\n                    borderColor: '#10B981'\n                  }].map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                    initial: {\n                      opacity: 0,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      scale: 1\n                    },\n                    transition: {\n                      delay: 1.2 + index * 0.1,\n                      duration: 0.6\n                    },\n                    whileHover: {\n                      scale: 1.05,\n                      y: -5\n                    },\n                    className: `bg-gradient-to-br ${stat.bgGradient} backdrop-blur-lg rounded-xl p-3 md:p-4 text-center relative overflow-hidden`,\n                    style: {\n                      border: `2px solid ${stat.borderColor}40`,\n                      boxShadow: `0 8px 32px ${stat.borderColor}20`\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1278,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(stat.icon, {\n                      className: \"w-6 h-6 md:w-8 md:h-8 mx-auto mb-2 relative z-10\",\n                      style: {\n                        color: stat.iconColor,\n                        filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1279,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-lg sm:text-xl md:text-2xl lg:text-3xl font-black mb-1 relative z-10\",\n                      style: {\n                        color: stat.iconColor,\n                        textShadow: `3px 3px 6px rgba(0,0,0,0.9)`,\n                        filter: 'drop-shadow(0 0 10px currentColor)',\n                        fontSize: 'clamp(1rem, 4vw, 2.5rem)'\n                      },\n                      children: stat.value\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1283,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs sm:text-sm font-bold relative z-10\",\n                      style: {\n                        color: '#FFFFFF',\n                        textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                        fontSize: 'clamp(0.75rem, 2vw, 1rem)'\n                      },\n                      children: stat.label\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1294,\n                      columnNumber: 23\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1266,\n                    columnNumber: 21\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1226,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1132,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1131,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1126,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1119,\n          columnNumber: 9\n        }, this), loading && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          className: \"flex flex-col items-center justify-center py-20\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            animate: {\n              rotate: 360\n            },\n            transition: {\n              duration: 2,\n              repeat: Infinity,\n              ease: \"linear\"\n            },\n            className: \"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1319,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-white/80 text-lg font-medium\",\n            children: \"Loading champions...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1324,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1314,\n          columnNumber: 11\n        }, this), !loading && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.3,\n            duration: 0.8\n          },\n          className: \"px-4 sm:px-6 md:px-8 lg:px-12 pb-20 md:pb-24 lg:pb-32\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [topPerformers.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                delay: 0.5,\n                duration: 0.8\n              },\n              className: \"mb-12\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-black text-center mb-6 md:mb-8 lg:mb-12 px-4\",\n                style: {\n                  background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                  textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                  filter: 'drop-shadow(0 0 15px #FFD700)'\n                },\n                children: \"\\uD83C\\uDFC6 CHAMPIONS PODIUM \\uD83C\\uDFC6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1346,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-end justify-center gap-4 sm:gap-6 md:gap-8 max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 mb-8\",\n                children: [topPerformers[1] && /*#__PURE__*/_jsxDEV(motion.div, {\n                  ref: user && topPerformers[1]._id === user._id ? podiumUserRef : null,\n                  \"data-user-id\": topPerformers[1]._id,\n                  \"data-user-rank\": 2,\n                  initial: {\n                    opacity: 0,\n                    x: -100,\n                    y: 50\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0,\n                    y: 0,\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  },\n                  transition: {\n                    delay: 0.8,\n                    duration: 1.2,\n                    scale: {\n                      duration: 4,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    },\n                    rotateY: {\n                      duration: 6,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  whileHover: {\n                    scale: 1.05,\n                    y: -10\n                  },\n                  className: `relative order-1 ${user && topPerformers[1]._id === user._id ? 'ring-2 ring-yellow-400' : ''} ${showFindMe && user && topPerformers[1]._id === user._id ? 'find-me-highlight' : ''}`,\n                  style: {\n                    height: '280px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute bottom-0 w-full h-20 bg-gradient-to-t from-gray-400 to-gray-300 rounded-t-lg border-2 border-gray-500 flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-2xl font-black text-gray-800\",\n                      children: \"2nd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1385,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1384,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `relative bg-gradient-to-br ${topPerformers[1].tier.color} p-1 rounded-xl ${topPerformers[1].tier.glow} shadow-xl mb-20`,\n                    style: {\n                      boxShadow: `0 6px 20px ${topPerformers[1].tier.shadowColor}50`,\n                      width: '200px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `${topPerformers[1].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`,\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1399,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-gray-300 to-gray-500 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20\",\n                        style: {\n                          color: '#000000',\n                          border: '2px solid #FFFFFF'\n                        },\n                        children: \"\\uD83E\\uDD48\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1402,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `relative mx-auto mb-3 ${user && topPerformers[1]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`,\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\",\n                          style: {\n                            background: '#f0f0f0',\n                            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                            width: '40px',\n                            height: '40px'\n                          },\n                          children: topPerformers[1].profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: topPerformers[1].profilePicture,\n                            alt: topPerformers[1].name,\n                            className: \"object-cover rounded-full w-full h-full\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1424,\n                            columnNumber: 35\n                          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"rounded-full flex items-center justify-center font-semibold w-full h-full\",\n                            style: {\n                              background: '#25D366',\n                              color: '#FFFFFF',\n                              fontSize: '16px'\n                            },\n                            children: topPerformers[1].name.charAt(0).toUpperCase()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1430,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1414,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1413,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-bold mb-2 truncate\",\n                        style: {\n                          color: topPerformers[1].tier.nameColor\n                        },\n                        children: topPerformers[1].name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1445,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-lg font-black mb-2\",\n                        style: {\n                          color: topPerformers[1].tier.textColor\n                        },\n                        children: [topPerformers[1].totalXP.toLocaleString(), \" XP\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1452,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex justify-center gap-3 text-xs\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: topPerformers[1].tier.textColor\n                          },\n                          children: [\"\\uD83E\\uDDE0 \", topPerformers[1].totalQuizzesTaken]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1457,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: topPerformers[1].tier.textColor\n                          },\n                          children: [\"\\uD83D\\uDD25 \", topPerformers[1].currentStreak]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1460,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1456,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1396,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1389,\n                    columnNumber: 25\n                  }, this)]\n                }, `second-${topPerformers[1]._id}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1360,\n                  columnNumber: 23\n                }, this), topPerformers[0] && /*#__PURE__*/_jsxDEV(motion.div, {\n                  ref: user && topPerformers[0]._id === user._id ? podiumUserRef : null,\n                  \"data-user-id\": topPerformers[0]._id,\n                  \"data-user-rank\": 1,\n                  initial: {\n                    opacity: 0,\n                    y: -100,\n                    scale: 0.8\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0,\n                    scale: 1,\n                    rotateY: [0, 10, -10, 0],\n                    y: [0, -10, 0]\n                  },\n                  transition: {\n                    delay: 0.5,\n                    duration: 1.5,\n                    rotateY: {\n                      duration: 8,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    },\n                    y: {\n                      duration: 4,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  whileHover: {\n                    scale: 1.08,\n                    y: -15\n                  },\n                  className: `relative order-2 z-10 ${user && topPerformers[0]._id === user._id ? 'ring-2 ring-yellow-400' : ''} ${showFindMe && user && topPerformers[0]._id === user._id ? 'find-me-highlight' : ''}`,\n                  style: {\n                    height: '320px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute bottom-0 w-full h-32 bg-gradient-to-t from-yellow-500 to-yellow-300 rounded-t-lg border-2 border-yellow-600 flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-3xl font-black text-yellow-900\",\n                      children: \"1st\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1496,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1495,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                    animate: {\n                      rotate: [0, 10, -10, 0],\n                      y: [0, -5, 0]\n                    },\n                    transition: {\n                      duration: 3,\n                      repeat: Infinity\n                    },\n                    className: \"absolute -top-16 left-1/2 transform -translate-x-1/2 z-30\",\n                    children: /*#__PURE__*/_jsxDEV(TbCrown, {\n                      className: \"w-16 h-16 text-yellow-400 drop-shadow-lg\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1505,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1500,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `relative bg-gradient-to-br ${topPerformers[0].tier.color} p-1.5 rounded-2xl ${topPerformers[0].tier.glow} shadow-2xl mb-32 transform scale-110`,\n                    style: {\n                      boxShadow: `0 8px 32px ${topPerformers[0].tier.shadowColor}60, 0 0 0 1px rgba(255,255,255,0.1)`,\n                      width: '240px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `${topPerformers[0].tier.bgColor} backdrop-blur-lg rounded-xl p-6 text-center relative overflow-hidden`,\n                      style: {\n                        background: `${topPerformers[0].tier.bgColor}, radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)`\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-xl\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1522,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute -top-4 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-full flex items-center justify-center font-black text-xl shadow-lg relative z-20\",\n                        style: {\n                          color: '#000000',\n                          textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                          border: '2px solid #FFFFFF'\n                        },\n                        children: \"\\uD83D\\uDC51\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1525,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `relative mx-auto mb-4 ${user && topPerformers[0]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`,\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\",\n                          style: {\n                            background: '#f0f0f0',\n                            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                            width: '48px',\n                            height: '48px'\n                          },\n                          children: topPerformers[0].profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: topPerformers[0].profilePicture,\n                            alt: topPerformers[0].name,\n                            className: \"object-cover rounded-full w-full h-full\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1548,\n                            columnNumber: 35\n                          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"rounded-full flex items-center justify-center font-semibold w-full h-full\",\n                            style: {\n                              background: '#25D366',\n                              color: '#FFFFFF',\n                              fontSize: '18px'\n                            },\n                            children: topPerformers[0].name.charAt(0).toUpperCase()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1554,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1538,\n                          columnNumber: 31\n                        }, this), user && topPerformers[0]._id === user._id && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"absolute -bottom-2 -right-2 rounded-full p-2 animate-pulse\",\n                          style: {\n                            background: 'linear-gradient(45deg, #fbbf24, #f59e0b)',\n                            boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                          },\n                          children: /*#__PURE__*/_jsxDEV(TbStar, {\n                            className: \"w-6 h-6 text-black\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1574,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1567,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1537,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-lg font-black mb-2 truncate px-2\",\n                        style: {\n                          color: topPerformers[0].tier.nameColor,\n                          textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                          filter: 'drop-shadow(0 0 8px currentColor)'\n                        },\n                        children: topPerformers[0].name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1580,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r ${topPerformers[0].tier.color} rounded-full text-sm font-black mb-3 relative z-10`,\n                        style: {\n                          background: `linear-gradient(135deg, ${topPerformers[0].tier.borderColor}, ${topPerformers[0].tier.textColor})`,\n                          color: '#FFFFFF',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                          boxShadow: `0 4px 15px ${topPerformers[0].tier.shadowColor}60`,\n                          border: '2px solid rgba(255,255,255,0.2)'\n                        },\n                        children: [topPerformers[0].tier.icon && /*#__PURE__*/React.createElement(topPerformers[0].tier.icon, {\n                          className: \"w-4 h-4\",\n                          style: {\n                            color: '#FFFFFF'\n                          }\n                        }), /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: '#FFFFFF'\n                          },\n                          children: topPerformers[0].tier.title\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1605,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1591,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"space-y-2 relative z-10\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-xl font-black\",\n                          style: {\n                            color: topPerformers[0].tier.nameColor,\n                            textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                            filter: 'drop-shadow(0 0 8px currentColor)'\n                          },\n                          children: [topPerformers[0].totalXP.toLocaleString(), \" XP\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1610,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex justify-center gap-4 text-sm\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center gap-1 justify-center\",\n                              children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                                className: \"w-4 h-4\",\n                                style: {\n                                  color: topPerformers[0].tier.textColor\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1621,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"font-bold\",\n                                style: {\n                                  color: topPerformers[0].tier.textColor\n                                },\n                                children: topPerformers[0].totalQuizzesTaken\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1622,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1620,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-xs opacity-80\",\n                              style: {\n                                color: topPerformers[0].tier.textColor\n                              },\n                              children: \"Quizzes\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1626,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1619,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center gap-1 justify-center\",\n                              children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n                                className: \"w-4 h-4\",\n                                style: {\n                                  color: '#FF6B35'\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1630,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"font-bold\",\n                                style: {\n                                  color: topPerformers[0].tier.textColor\n                                },\n                                children: topPerformers[0].currentStreak\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1631,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1629,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-xs opacity-80\",\n                              style: {\n                                color: topPerformers[0].tier.textColor\n                              },\n                              children: \"Streak\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1635,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1628,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1618,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1609,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1516,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1509,\n                    columnNumber: 25\n                  }, this)]\n                }, `first-${topPerformers[0]._id}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1471,\n                  columnNumber: 23\n                }, this), topPerformers[2] && /*#__PURE__*/_jsxDEV(motion.div, {\n                  ref: user && topPerformers[2]._id === user._id ? podiumUserRef : null,\n                  \"data-user-id\": topPerformers[2]._id,\n                  \"data-user-rank\": 3,\n                  initial: {\n                    opacity: 0,\n                    x: 100,\n                    y: 50\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0,\n                    y: 0,\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, -5, 0]\n                  },\n                  transition: {\n                    delay: 1.0,\n                    duration: 1.2,\n                    scale: {\n                      duration: 4,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    },\n                    rotateY: {\n                      duration: 6,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  whileHover: {\n                    scale: 1.05,\n                    y: -10\n                  },\n                  className: `relative order-3 ${user && topPerformers[2]._id === user._id ? 'ring-2 ring-yellow-400' : ''} ${showFindMe && user && topPerformers[2]._id === user._id ? 'find-me-highlight' : ''}`,\n                  style: {\n                    height: '280px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute bottom-0 w-full h-16 bg-gradient-to-t from-amber-600 to-amber-400 rounded-t-lg border-2 border-amber-700 flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xl font-black text-amber-900\",\n                      children: \"3rd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1671,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1670,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `relative bg-gradient-to-br ${topPerformers[2].tier.color} p-1 rounded-xl ${topPerformers[2].tier.glow} shadow-xl mb-16`,\n                    style: {\n                      boxShadow: `0 6px 20px ${topPerformers[2].tier.shadowColor}50`,\n                      width: '200px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `${topPerformers[2].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`,\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1685,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-amber-600 to-amber-800 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20\",\n                        style: {\n                          color: '#000000',\n                          border: '2px solid #FFFFFF'\n                        },\n                        children: \"\\uD83E\\uDD49\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1688,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `relative mx-auto mb-3 ${user && topPerformers[2]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`,\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\",\n                          style: {\n                            background: '#f0f0f0',\n                            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                            width: '40px',\n                            height: '40px'\n                          },\n                          children: topPerformers[2].profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: topPerformers[2].profilePicture,\n                            alt: topPerformers[2].name,\n                            className: \"object-cover rounded-full w-full h-full\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1710,\n                            columnNumber: 35\n                          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"rounded-full flex items-center justify-center font-semibold w-full h-full\",\n                            style: {\n                              background: '#25D366',\n                              color: '#FFFFFF',\n                              fontSize: '16px'\n                            },\n                            children: topPerformers[2].name.charAt(0).toUpperCase()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1716,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1700,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1699,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-bold mb-2 truncate\",\n                        style: {\n                          color: topPerformers[2].tier.nameColor\n                        },\n                        children: topPerformers[2].name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1731,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-lg font-black mb-2\",\n                        style: {\n                          color: topPerformers[2].tier.textColor\n                        },\n                        children: [topPerformers[2].totalXP.toLocaleString(), \" XP\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1738,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex justify-center gap-3 text-xs\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: topPerformers[2].tier.textColor\n                          },\n                          children: [\"\\uD83E\\uDDE0 \", topPerformers[2].totalQuizzesTaken]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1743,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: topPerformers[2].tier.textColor\n                          },\n                          children: [\"\\uD83D\\uDD25 \", topPerformers[2].currentStreak]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1746,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1742,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1682,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1675,\n                    columnNumber: 25\n                  }, this)]\n                }, `third-${topPerformers[2]._id}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1646,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1357,\n                columnNumber: 19\n              }, this), currentUserLeague && /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: 1.3,\n                  duration: 0.8\n                },\n                className: \"mt-8 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-indigo-500/20 backdrop-blur-lg rounded-2xl p-6 border border-blue-400/30 max-w-4xl mx-auto\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-bold text-white mb-2\",\n                    children: [\"Your League: \", currentUserLeague.league.leagueIcon, \" \", currentUserLeague.league.title]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1765,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-white/80 text-sm\",\n                    children: [\"Rank #\", currentUserLeague.userRank, \" of \", currentUserLeague.totalInLeague, \" in your league\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1768,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1764,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-center gap-4 text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-green-500/20 rounded-lg p-3 text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-green-400 font-bold\",\n                      children: currentUserLeague.league.promotionXP > 0 ? `${currentUserLeague.league.promotionXP - ((user === null || user === void 0 ? void 0 : user.totalXP) || 0)} XP` : 'Max League'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1775,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80 text-xs\",\n                      children: \"To Promotion\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1781,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1774,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-blue-500/20 rounded-lg p-3 text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-blue-400 font-bold\",\n                      children: currentUserLeague.totalInLeague\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1784,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80 text-xs\",\n                      children: \"League Members\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1785,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1783,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-purple-500/20 rounded-lg p-3 text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-purple-400 font-bold\",\n                      children: [\"#\", currentUserLeague.userRank]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1788,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80 text-xs\",\n                      children: \"League Rank\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1789,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1787,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1773,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1758,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1340,\n              columnNumber: 17\n            }, this), (showLeagueView ? leagueUsers : otherPerformers).length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 1,\n                duration: 0.8\n              },\n              className: \"mt-16 main-ranking-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center mb-8 md:mb-12\",\n                children: [/*#__PURE__*/_jsxDEV(motion.h2, {\n                  className: \"text-2xl sm:text-3xl md:text-4xl font-black mb-3\",\n                  style: {\n                    background: showLeagueView ? 'linear-gradient(45deg, #10B981, #059669, #047857)' : 'linear-gradient(45deg, #8B5CF6, #06B6D4, #10B981)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    filter: showLeagueView ? 'drop-shadow(0 0 12px #10B981)' : 'drop-shadow(0 0 12px #8B5CF6)'\n                  },\n                  animate: {\n                    scale: [1, 1.01, 1]\n                  },\n                  transition: {\n                    duration: 4,\n                    repeat: Infinity\n                  },\n                  children: showLeagueView ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [currentUserLeague === null || currentUserLeague === void 0 ? void 0 : currentUserLeague.league.leagueIcon, \" \", currentUserLeague === null || currentUserLeague === void 0 ? void 0 : currentUserLeague.league.title, \" LEAGUE \", currentUserLeague === null || currentUserLeague === void 0 ? void 0 : currentUserLeague.league.leagueIcon]\n                  }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: \"\\u26A1 LEADERBOARD \\u26A1\"\n                  }, void 0, false)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1809,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-white/70 text-sm md:text-base font-medium\",\n                  children: showLeagueView ? `Your league with ${(currentUserLeague === null || currentUserLeague === void 0 ? void 0 : currentUserLeague.totalInLeague) || 0} members` : 'Compete with the best minds in the academy'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1831,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1808,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"max-w-6xl mx-auto px-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid gap-3 md:gap-4\",\n                  children: (showLeagueView ? leagueUsers : otherPerformers).map((champion, index) => {\n                    const actualRank = showLeagueView ? index + 1 // League ranking starts from 1\n                    : index + 4; // Global ranking starts from 4 (after top 3)\n                    const isCurrentUser = user && champion._id === user._id;\n                    return /*#__PURE__*/_jsxDEV(motion.div, {\n                      ref: isCurrentUser ? listUserRef : null,\n                      \"data-user-id\": champion._id,\n                      \"data-user-rank\": actualRank,\n                      initial: {\n                        opacity: 0,\n                        y: 20\n                      },\n                      animate: {\n                        opacity: 1,\n                        y: 0\n                      },\n                      transition: {\n                        delay: 1.2 + index * 0.05,\n                        duration: 0.4\n                      },\n                      whileHover: {\n                        scale: 1.01,\n                        y: -2\n                      },\n                      className: `ranking-card group relative ${isCurrentUser ? 'ring-2 ring-yellow-400/60' : ''} ${showFindMe && isCurrentUser ? 'find-me-highlight' : ''}`,\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `bg-gradient-to-r ${champion.tier.color} p-0.5 rounded-2xl ${champion.tier.glow} transition-all duration-300 group-hover:scale-[1.01]`,\n                        style: {\n                          boxShadow: `0 4px 20px ${champion.tier.shadowColor}40`\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `${champion.tier.bgColor} backdrop-blur-xl rounded-2xl p-4 flex items-center gap-4 relative overflow-hidden`,\n                          style: {\n                            border: `1px solid ${champion.tier.borderColor}30`\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-r from-white/3 to-transparent rounded-2xl\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1874,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center gap-3 flex-shrink-0\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"relative\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center font-black text-sm shadow-lg relative z-10\",\n                                style: {\n                                  color: '#FFFFFF',\n                                  textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                  border: '2px solid rgba(255,255,255,0.2)',\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                                },\n                                children: [\"#\", actualRank]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1880,\n                                columnNumber: 35\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"absolute -top-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center text-xs\",\n                                style: {\n                                  background: champion.tier.borderColor,\n                                  color: '#FFFFFF',\n                                  fontSize: '8px'\n                                },\n                                children: champion.tier.icon && /*#__PURE__*/_jsxDEV(champion.tier.icon, {\n                                  className: \"w-2 h-2\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1900,\n                                  columnNumber: 60\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1892,\n                                columnNumber: 35\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1879,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"relative\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"rounded-full overflow-hidden border-2 border-white/20 relative\",\n                                style: {\n                                  background: '#f0f0f0',\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                  width: '32px',\n                                  height: '32px',\n                                  minWidth: '32px',\n                                  minHeight: '32px',\n                                  maxWidth: '32px',\n                                  maxHeight: '32px'\n                                },\n                                children: champion.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                                  src: champion.profilePicture,\n                                  alt: champion.name,\n                                  className: \"object-cover rounded-full\",\n                                  style: {\n                                    objectFit: 'cover',\n                                    objectPosition: 'center',\n                                    width: '32px',\n                                    height: '32px',\n                                    minWidth: '32px',\n                                    minHeight: '32px',\n                                    maxWidth: '32px',\n                                    maxHeight: '32px',\n                                    borderRadius: '50%'\n                                  }\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1920,\n                                  columnNumber: 39\n                                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"rounded-full flex items-center justify-center font-semibold\",\n                                  style: {\n                                    background: '#25D366',\n                                    color: '#FFFFFF',\n                                    fontSize: '12px',\n                                    width: '32px',\n                                    height: '32px',\n                                    minWidth: '32px',\n                                    minHeight: '32px',\n                                    maxWidth: '32px',\n                                    maxHeight: '32px',\n                                    borderRadius: '50%'\n                                  },\n                                  children: champion.name.charAt(0).toUpperCase()\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1937,\n                                  columnNumber: 39\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1906,\n                                columnNumber: 35\n                              }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"absolute -top-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse\",\n                                style: {\n                                  background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                  boxShadow: '0 2px 6px rgba(255,215,0,0.6)'\n                                },\n                                children: /*#__PURE__*/_jsxDEV(TbStar, {\n                                  className: \"w-2.5 h-2.5 text-black\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1965,\n                                  columnNumber: 39\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1958,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1905,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1877,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex-1 min-w-0 px-2\",\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"space-y-1\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center gap-2 mb-1\",\n                                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                                  className: \"text-base md:text-lg font-bold truncate\",\n                                  style: {\n                                    color: champion.tier.nameColor,\n                                    textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                    filter: 'drop-shadow(0 0 4px currentColor)'\n                                  },\n                                  children: champion.name\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1976,\n                                  columnNumber: 37\n                                }, this), isCurrentUser && /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"px-2 py-0.5 rounded-full text-xs font-bold\",\n                                  style: {\n                                    background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                    color: '#000000',\n                                    boxShadow: '0 2px 4px rgba(255,215,0,0.4)'\n                                  },\n                                  children: \"YOU\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1987,\n                                  columnNumber: 39\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1975,\n                                columnNumber: 35\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: `league-badge inline-flex items-center gap-1.5 px-3 py-1 bg-gradient-to-r ${champion.tier.color} rounded-lg text-xs font-bold`,\n                                style: {\n                                  color: '#FFFFFF',\n                                  textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                  border: `1px solid ${champion.tier.borderColor}`,\n                                  boxShadow: `0 2px 6px ${champion.tier.shadowColor}40`\n                                },\n                                children: [/*#__PURE__*/_jsxDEV(champion.tier.icon, {\n                                  className: \"w-3 h-3\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2010,\n                                  columnNumber: 37\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"text-xs font-medium\",\n                                  children: champion.tier.title\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2011,\n                                  columnNumber: 37\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2001,\n                                columnNumber: 35\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"text-xs text-white/70 mt-0.5\",\n                                children: [champion.level, \" \\u2022 Class \", champion.class]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2015,\n                                columnNumber: 35\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1973,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1972,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex flex-col items-end gap-1 flex-shrink-0\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-lg md:text-xl font-black mb-2\",\n                              style: {\n                                color: champion.tier.nameColor,\n                                textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                filter: 'drop-shadow(0 0 6px currentColor)'\n                              },\n                              children: [champion.totalXP.toLocaleString(), \" XP\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2024,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center gap-3 text-xs\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center gap-1 px-2 py-1 rounded-md\",\n                                style: {\n                                  backgroundColor: `${champion.tier.borderColor}20`,\n                                  color: champion.tier.textColor\n                                },\n                                children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                                  className: \"w-3 h-3\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2044,\n                                  columnNumber: 37\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"font-medium\",\n                                  children: champion.totalQuizzesTaken\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2045,\n                                  columnNumber: 37\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2037,\n                                columnNumber: 35\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center gap-1 px-2 py-1 rounded-md\",\n                                style: {\n                                  backgroundColor: '#FF6B3520',\n                                  color: '#FF6B35'\n                                },\n                                children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n                                  className: \"w-3 h-3\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2054,\n                                  columnNumber: 37\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"font-medium\",\n                                  children: champion.currentStreak\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2055,\n                                  columnNumber: 37\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2047,\n                                columnNumber: 35\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2036,\n                              columnNumber: 33\n                            }, this), (() => {\n                              const badge = getSubscriptionBadge(champion.subscriptionStatus, champion.subscriptionEndDate, champion.subscriptionPlan, champion.activePlanTitle, actualRank);\n                              return /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"inline-flex items-center gap-1 px-2 py-0.5 rounded-md text-xs font-medium mt-2\",\n                                style: {\n                                  backgroundColor: badge.bgColor,\n                                  color: badge.color,\n                                  border: `1px solid ${badge.borderColor}`,\n                                  fontSize: '10px'\n                                },\n                                children: badge.text\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2069,\n                                columnNumber: 37\n                              }, this);\n                            })()]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2022,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1867,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1861,\n                        columnNumber: 27\n                      }, this)\n                    }, champion._id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1849,\n                      columnNumber: 25\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1841,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1840,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1801,\n              columnNumber: 17\n            }, this), rankingData.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 1.8,\n                duration: 0.8\n              },\n              className: \"mt-12 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-green-500/20 backdrop-blur-lg rounded-2xl p-6 border border-blue-400/30\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-bold mb-4\",\n                  style: {\n                    color: '#60A5FA',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  },\n                  children: \"\\uD83D\\uDCCA Real User Data Integration\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2102,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-green-500/20 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-green-400 font-bold text-lg\",\n                      children: rankingData.filter(u => u.dataSource === 'reports').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2109,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80\",\n                      children: \"\\uD83D\\uDCCA Live Quiz Data\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2112,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2108,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-blue-500/20 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-blue-400 font-bold text-lg\",\n                      children: rankingData.filter(u => u.dataSource === 'legacy_points').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2115,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80\",\n                      children: \"\\uD83D\\uDCC8 Legacy Points\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2118,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2114,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-purple-500/20 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-purple-400 font-bold text-lg\",\n                      children: rankingData.filter(u => u.dataSource === 'estimated').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2121,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-white/80\",\n                      children: \"\\uD83D\\uDD2E Estimated Stats\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2124,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2120,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2107,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-white/70 text-sm mt-4\",\n                  children: \"Using real database users (admins excluded) with intelligent data processing\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2127,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2101,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2095,\n              columnNumber: 17\n            }, this), currentUserRank && currentUserRank > 3 && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                delay: 1.5,\n                duration: 0.8\n              },\n              className: \"mt-12 bg-gradient-to-r from-yellow-500/20 via-orange-500/20 to-red-500/20 backdrop-blur-lg rounded-2xl p-6 border border-yellow-400/30\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-2xl font-bold mb-2\",\n                  style: {\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  },\n                  children: \"Your Current Position\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2143,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-6xl font-black mb-2\",\n                  style: {\n                    color: '#fbbf24',\n                    textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                    fontWeight: '900'\n                  },\n                  children: [\"#\", currentUserRank]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2148,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-lg\",\n                  style: {\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  },\n                  children: \"You're doing amazing! Keep pushing forward to reach the podium! \\uD83D\\uDE80\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2153,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2142,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2136,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 2,\n                duration: 0.8\n              },\n              className: \"mt-16 text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-8 border border-white/10\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  animate: {\n                    scale: [1, 1.05, 1]\n                  },\n                  transition: {\n                    duration: 3,\n                    repeat: Infinity\n                  },\n                  children: /*#__PURE__*/_jsxDEV(TbRocket, {\n                    className: \"w-16 h-16 text-yellow-400 mx-auto mb-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2176,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2172,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-3xl font-bold mb-4\",\n                  style: {\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  },\n                  children: \"Ready to Rise Higher?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2178,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xl mb-6 max-w-2xl mx-auto\",\n                  style: {\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  },\n                  children: \"Every quiz you take, every challenge you conquer, brings you closer to greatness. Your journey to the top starts with the next question!\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2183,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  className: \"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300\",\n                  onClick: () => window.location.href = '/user/quiz',\n                  children: \"Take a Quiz Now! \\uD83C\\uDFAF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2191,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2171,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2165,\n              columnNumber: 15\n            }, this), rankingData.length === 0 && !loading && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              className: \"text-center py-20\",\n              children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-24 h-24 text-white/30 mx-auto mb-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2209,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold mb-4\",\n                style: {\n                  color: '#ffffff',\n                  textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                  fontWeight: '800'\n                },\n                children: \"No Champions Yet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2210,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg\",\n                style: {\n                  color: '#e5e7eb',\n                  textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                  fontWeight: '600'\n                },\n                children: \"Be the first to take a quiz and claim your spot in the Hall of Champions!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2215,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2204,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1336,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1330,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 971,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 938,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(AmazingRankingPage, \"GGr6N+C1XEiiYS3O1S78Ne7Ds1c=\", false, function () {\n  return [useSelector, useNavigate];\n});\n_c = AmazingRankingPage;\nexport default AmazingRankingPage;\nvar _c;\n$RefreshReg$(_c, \"AmazingRankingPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "motion", "AnimatePresence", "useSelector", "useNavigate", "message", "TbTrophy", "TbCrown", "TbStar", "TbFlame", "TbTarget", "TbBrain", "TbHome", "TbRefresh", "TbMedal", "TbBolt", "TbRocket", "TbDiamond", "TbHeart", "TbEye", "TbUsers", "TbTrendingUp", "TbAward", "TbShield", "getAllReportsForRanking", "getXPLeaderboard", "getUserRanking", "getAllUsers", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AmazingRankingPage", "_s", "userState", "state", "users", "user", "navigate", "rankingData", "setRankingData", "loading", "setLoading", "currentUserRank", "setCurrentUserRank", "viewMode", "setViewMode", "showStats", "setShowStats", "animationPhase", "setAnimationPhase", "motivationalQuote", "setMotivationalQuote", "showFindMe", "setShowFindMe", "currentUserLeague", "setCurrentUserLeague", "leagueUsers", "setLeagueUsers", "showLeagueView", "setShowLeagueView", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedLeague", "leagueGroups", "setLeagueGroups", "headerRef", "currentUserRef", "podiumUserRef", "listUserRef", "motivationalQuotes", "leagueSystem", "mythic", "min", "color", "bgColor", "textColor", "nameColor", "shadowColor", "glow", "icon", "title", "description", "borderColor", "effect", "leagueIcon", "promotionXP", "relegationXP", "maxUsers", "legendary", "diamond", "platinum", "gold", "silver", "bronze", "rookie", "getUserLeague", "xp", "league", "config", "Object", "entries", "groupUsersByLeague", "leagues", "for<PERSON>ach", "userLeague", "totalXP", "push", "tier", "keys", "leagueKey", "sort", "a", "b", "getCurrentUserLeagueData", "allUsers", "currentUser", "filter", "userRank", "findIndex", "u", "_id", "totalInLeague", "length", "handleLeagueSelect", "_leagueGroups$leagueK", "getOrderedLeagues", "leagueOrder", "fetchRankingData", "console", "log", "xpLeaderboardResponse", "limit", "levelFilter", "level", "includeInactive", "success", "data", "filteredData", "userData", "totalQuizzesTaken", "transformedData", "map", "index", "name", "email", "class", "profilePicture", "profileImage", "averageScore", "currentStreak", "bestStreak", "subscriptionStatus", "rank", "isRealUser", "rankingScore", "currentLevel", "xpToNextLevel", "lifetimeXP", "seasonXP", "achievements", "dataSource", "userRankIndex", "item", "userLeagueData", "grouped", "xpError", "rankingResponse", "usersResponse", "error", "userError", "userReportsMap", "_item$user", "userId", "reports", "role", "userReports", "totalQuizzes", "totalScore", "reduce", "sum", "report", "score", "Math", "round", "totalPoints", "estimatedQuizzes", "max", "floor", "estimatedAverage", "tempStreak", "pointsPerQuiz", "originalPoints", "hasReports", "String", "userIdType", "isAdmin", "userXP", "userRankPosition", "totalRankedUsers", "firstFewUserIds", "slice", "id", "type", "exactMatch", "find", "stringMatch", "nameMatch", "dataSources", "legacy_points", "estimated", "quizzes", "avg", "source", "warning", "randomQuote", "random", "animationTimer", "setInterval", "prev", "handleWindowFocus", "handleRankingUpdate", "event", "detail", "setTimeout", "window", "addEventListener", "clearInterval", "removeEventListener", "topPerformers", "otherPerformers", "handleFindMe", "mainSection", "document", "querySelector", "scrollIntoView", "behavior", "block", "possibleSelectors", "found", "selector", "items", "querySelectorAll", "targetIndex", "targetItem", "originalStyle", "border", "style", "boxShadow", "transform", "backgroundColor", "outline", "transition", "zIndex", "pulseCount", "pulseInterval", "info", "estimatedPosition", "innerHeight", "scrollTo", "top", "getSubscriptionBadge", "subscriptionEndDate", "subscriptionPlan", "activePlanTitle", "userIndex", "now", "Date", "endDate", "isActive", "text", "className", "children", "div", "initial", "opacity", "animate", "rotate", "duration", "repeat", "Infinity", "ease", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "jsx", "Array", "_", "i", "y", "x", "delay", "left", "button", "whileHover", "scale", "whileTap", "onClick", "fontSize", "innerWidth", "background", "textShadow", "fontWeight", "current", "userElements", "el", "getAttribute", "disabled", "rotateY", "span", "backgroundPosition", "backgroundSize", "WebkitBackgroundClip", "WebkitTextFillColor", "p", "fontStyle", "value", "label", "bgGradient", "iconColor", "toLocaleString", "stat", "ref", "height", "width", "src", "alt", "char<PERSON>t", "toUpperCase", "createElement", "h2", "champion", "actualRank", "isCurrentUser", "min<PERSON><PERSON><PERSON>", "minHeight", "max<PERSON><PERSON><PERSON>", "maxHeight", "objectFit", "objectPosition", "borderRadius", "badge", "location", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Ranking/index.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { message } from 'antd';\nimport {\n  TbTrophy,\n  TbCrown,\n  TbStar,\n  TbFlame,\n  TbTarget,\n  TbBrain,\n  TbHome,\n  TbRefresh,\n  TbMedal,\n  TbBolt,\n  TbRocket,\n  TbDiamond,\n  TbHeart,\n  TbEye,\n  TbUsers,\n  TbTrendingUp,\n  TbAward,\n  TbShield\n} from 'react-icons/tb';\nimport { getAllReportsForRanking, getXPLeaderboard, getUserRanking } from '../../../apicalls/reports';\nimport { getAllUsers } from '../../../apicalls/users';\n\nconst AmazingRankingPage = () => {\n  const userState = useSelector((state) => state.users || {});\n  const user = userState.user || null;\n  const navigate = useNavigate();\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [viewMode, setViewMode] = useState('global');\n  const [showStats, setShowStats] = useState(true);\n  const [animationPhase, setAnimationPhase] = useState(0);\n  const [motivationalQuote, setMotivationalQuote] = useState('');\n  const [showFindMe, setShowFindMe] = useState(false);\n  const [currentUserLeague, setCurrentUserLeague] = useState(null);\n  const [leagueUsers, setLeagueUsers] = useState([]);\n  const [showLeagueView, setShowLeagueView] = useState(false);\n  const [selectedLeague, setSelectedLeague] = useState(null);\n  const [leagueGroups, setLeagueGroups] = useState({});\n  const headerRef = useRef(null);\n  const currentUserRef = useRef(null);\n  const podiumUserRef = useRef(null);\n  const listUserRef = useRef(null);\n\n  // Motivational quotes for different performance levels\n  const motivationalQuotes = [\n    \"🚀 Every expert was once a beginner. Keep climbing!\",\n    \"⭐ Your potential is endless. Show them what you're made of!\",\n    \"🔥 Champions are made in the moments when nobody's watching.\",\n    \"💎 Pressure makes diamonds. You're becoming brilliant!\",\n    \"🎯 Success is not final, failure is not fatal. Keep going!\",\n    \"⚡ The only impossible journey is the one you never begin.\",\n    \"🌟 Believe in yourself and all that you are capable of!\",\n    \"🏆 Greatness is not about being better than others, it's about being better than yesterday.\",\n    \"💪 Your only limit is your mind. Break through it!\",\n    \"🎨 Paint your success with the colors of determination!\"\n  ];\n\n  // Enhanced League System with Duolingo-style progression\n  const leagueSystem = {\n    mythic: {\n      min: 50000,\n      color: 'from-purple-300 via-pink-300 via-red-300 to-orange-300',\n      bgColor: 'bg-gradient-to-br from-purple-900/50 via-pink-900/50 to-red-900/50',\n      textColor: '#FFD700',\n      nameColor: '#FF1493',\n      shadowColor: 'rgba(255, 20, 147, 0.9)',\n      glow: 'shadow-pink-500/90',\n      icon: TbCrown,\n      title: 'MYTHIC',\n      description: 'Legendary Master',\n      borderColor: '#FF1493',\n      effect: 'mythic-aura',\n      leagueIcon: '👑',\n      promotionXP: 0, // Max league\n      relegationXP: 40000,\n      maxUsers: 10\n    },\n    legendary: {\n      min: 25000,\n      color: 'from-purple-400 via-indigo-400 via-blue-400 to-cyan-400',\n      bgColor: 'bg-gradient-to-br from-purple-900/40 via-indigo-900/40 to-blue-900/40',\n      textColor: '#8A2BE2',\n      nameColor: '#9370DB',\n      shadowColor: 'rgba(138, 43, 226, 0.9)',\n      glow: 'shadow-purple-500/80',\n      icon: TbDiamond,\n      title: 'LEGENDARY',\n      description: 'Elite Champion',\n      borderColor: '#8A2BE2',\n      effect: 'legendary-sparkle',\n      leagueIcon: '💎',\n      promotionXP: 50000,\n      relegationXP: 20000,\n      maxUsers: 25\n    },\n    diamond: {\n      min: 12000,\n      color: 'from-cyan-300 via-blue-300 via-indigo-300 to-purple-300',\n      bgColor: 'bg-gradient-to-br from-cyan-900/40 via-blue-900/40 to-indigo-900/40',\n      textColor: '#00CED1',\n      nameColor: '#40E0D0',\n      shadowColor: 'rgba(0, 206, 209, 0.9)',\n      glow: 'shadow-cyan-400/80',\n      icon: TbShield,\n      title: 'DIAMOND',\n      description: 'Expert Level',\n      borderColor: '#00CED1',\n      effect: 'diamond-shine',\n      leagueIcon: '🛡️',\n      promotionXP: 25000,\n      relegationXP: 8000,\n      maxUsers: 50\n    },\n    platinum: {\n      min: 6000,\n      color: 'from-slate-300 via-gray-300 via-zinc-300 to-stone-300',\n      bgColor: 'bg-gradient-to-br from-slate-800/40 via-gray-800/40 to-zinc-800/40',\n      textColor: '#C0C0C0',\n      nameColor: '#D3D3D3',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-slate-400/80',\n      icon: TbAward,\n      title: 'PLATINUM',\n      description: 'Advanced',\n      borderColor: '#C0C0C0',\n      effect: 'platinum-gleam',\n      leagueIcon: '🏆',\n      promotionXP: 12000,\n      relegationXP: 4000,\n      maxUsers: 100\n    },\n    gold: {\n      min: 3000,\n      color: 'from-yellow-300 via-amber-300 via-orange-300 to-red-300',\n      bgColor: 'bg-gradient-to-br from-yellow-900/40 via-amber-900/40 to-orange-900/40',\n      textColor: '#FFD700',\n      nameColor: '#FFA500',\n      shadowColor: 'rgba(255, 215, 0, 0.9)',\n      glow: 'shadow-yellow-400/80',\n      icon: TbTrophy,\n      title: 'GOLD',\n      description: 'Skilled',\n      borderColor: '#FFD700',\n      effect: 'gold-glow',\n      leagueIcon: '🥇',\n      promotionXP: 6000,\n      relegationXP: 2000,\n      maxUsers: 200\n    },\n    silver: {\n      min: 1500,\n      color: 'from-gray-300 via-slate-300 via-zinc-300 to-gray-300',\n      bgColor: 'bg-gradient-to-br from-gray-800/40 via-slate-800/40 to-zinc-800/40',\n      textColor: '#C0C0C0',\n      nameColor: '#B8B8B8',\n      shadowColor: 'rgba(192, 192, 192, 0.9)',\n      glow: 'shadow-gray-400/80',\n      icon: TbMedal,\n      title: 'SILVER',\n      description: 'Improving',\n      borderColor: '#C0C0C0',\n      effect: 'silver-shimmer',\n      leagueIcon: '🥈',\n      promotionXP: 3000,\n      relegationXP: 800,\n      maxUsers: 300\n    },\n    bronze: {\n      min: 500,\n      color: 'from-orange-300 via-amber-300 via-yellow-300 to-orange-300',\n      bgColor: 'bg-gradient-to-br from-orange-900/40 via-amber-900/40 to-yellow-900/40',\n      textColor: '#CD7F32',\n      nameColor: '#D2691E',\n      shadowColor: 'rgba(205, 127, 50, 0.9)',\n      glow: 'shadow-orange-400/80',\n      icon: TbStar,\n      title: 'BRONZE',\n      description: 'Learning',\n      borderColor: '#CD7F32',\n      effect: 'bronze-warm',\n      leagueIcon: '🥉',\n      promotionXP: 1500,\n      relegationXP: 200,\n      maxUsers: 500\n    },\n    rookie: {\n      min: 0,\n      color: 'from-green-300 via-emerald-300 via-teal-300 to-cyan-300',\n      bgColor: 'bg-gradient-to-br from-green-900/40 via-emerald-900/40 to-teal-900/40',\n      textColor: '#32CD32',\n      nameColor: '#90EE90',\n      shadowColor: 'rgba(50, 205, 50, 0.9)',\n      glow: 'shadow-green-400/80',\n      icon: TbRocket,\n      title: 'ROOKIE',\n      description: 'Starting Out',\n      borderColor: '#32CD32',\n      effect: 'rookie-glow',\n      leagueIcon: '🚀',\n      promotionXP: 500,\n      relegationXP: 0, // Can't be relegated from rookie\n      maxUsers: 1000\n    }\n  };\n\n  // Get user's league based on XP with enhanced progression\n  const getUserLeague = (xp) => {\n    for (const [league, config] of Object.entries(leagueSystem)) {\n      if (xp >= config.min) return { league, ...config };\n    }\n    return { league: 'rookie', ...leagueSystem.rookie };\n  };\n\n  // Group users by their leagues for better organization\n  const groupUsersByLeague = (users) => {\n    const leagues = {};\n\n    users.forEach(user => {\n      const userLeague = getUserLeague(user.totalXP);\n      if (!leagues[userLeague.league]) {\n        leagues[userLeague.league] = {\n          config: userLeague,\n          users: []\n        };\n      }\n      leagues[userLeague.league].users.push({\n        ...user,\n        tier: userLeague // Update to use league instead of tier\n      });\n    });\n\n    // Sort users within each league by XP\n    Object.keys(leagues).forEach(leagueKey => {\n      leagues[leagueKey].users.sort((a, b) => b.totalXP - a.totalXP);\n    });\n\n    return leagues;\n  };\n\n  // Get current user's league and friends in the same league\n  const getCurrentUserLeagueData = (allUsers, currentUser) => {\n    if (!currentUser) return null;\n\n    const userLeague = getUserLeague(currentUser.totalXP || 0);\n    const leagueUsers = allUsers.filter(user => {\n      const league = getUserLeague(user.totalXP);\n      return league.league === userLeague.league;\n    }).sort((a, b) => b.totalXP - a.totalXP);\n\n    return {\n      league: userLeague,\n      users: leagueUsers,\n      userRank: leagueUsers.findIndex(u => u._id === currentUser._id) + 1,\n      totalInLeague: leagueUsers.length\n    };\n  };\n\n  // Handle league selection\n  const handleLeagueSelect = (leagueKey) => {\n    if (selectedLeague === leagueKey) {\n      setSelectedLeague(null);\n      setShowLeagueView(false);\n    } else {\n      setSelectedLeague(leagueKey);\n      setShowLeagueView(true);\n      setLeagueUsers(leagueGroups[leagueKey]?.users || []);\n    }\n  };\n\n  // Get ordered league keys from best to worst\n  const getOrderedLeagues = () => {\n    const leagueOrder = ['mythic', 'legendary', 'diamond', 'platinum', 'gold', 'silver', 'bronze', 'rookie'];\n    return leagueOrder.filter(league => leagueGroups[league] && leagueGroups[league].users.length > 0);\n  };\n\n  // Fetch ranking data using enhanced XP system\n  const fetchRankingData = async () => {\n    try {\n      setLoading(true);\n      console.log('🚀 Fetching enhanced XP ranking data...');\n\n      // Try the new XP-based leaderboard first\n      try {\n        console.log('📊 Fetching XP leaderboard...');\n        const xpLeaderboardResponse = await getXPLeaderboard({\n          limit: 1000,\n          levelFilter: user?.level || 'all',\n          includeInactive: false\n        });\n\n        console.log('✨ XP Leaderboard response:', xpLeaderboardResponse);\n\n        if (xpLeaderboardResponse && xpLeaderboardResponse.success && xpLeaderboardResponse.data) {\n          console.log('🎯 Using enhanced XP ranking data');\n\n          // Filter to only include users who have actually taken quizzes and earned XP\n          const filteredData = xpLeaderboardResponse.data.filter(userData =>\n            (userData.totalXP && userData.totalXP > 0) ||\n            (userData.totalQuizzesTaken && userData.totalQuizzesTaken > 0)\n          );\n\n          const transformedData = filteredData.map((userData, index) => ({\n            _id: userData._id,\n            name: userData.name || 'Anonymous Champion',\n            email: userData.email || '',\n            class: userData.class || '',\n            level: userData.level || '',\n            profilePicture: userData.profileImage || '',\n            totalXP: userData.totalXP || 0,\n            totalQuizzesTaken: userData.totalQuizzesTaken || 0,\n            averageScore: userData.averageScore || 0,\n            currentStreak: userData.currentStreak || 0,\n            bestStreak: userData.bestStreak || 0,\n            subscriptionStatus: userData.subscriptionStatus || 'free',\n            rank: index + 1,\n            tier: getUserLeague(userData.totalXP || 0),\n            isRealUser: true,\n            rankingScore: userData.rankingScore || 0,\n            // Enhanced XP data\n            currentLevel: userData.currentLevel || 1,\n            xpToNextLevel: userData.xpToNextLevel || 100,\n            lifetimeXP: userData.lifetimeXP || 0,\n            seasonXP: userData.seasonXP || 0,\n            achievements: userData.achievements || [],\n            dataSource: 'enhanced_xp'\n          }));\n\n          setRankingData(transformedData);\n\n          // Find current user's rank\n          const userRankIndex = transformedData.findIndex(item => item._id === user?._id);\n          setCurrentUserRank(userRankIndex >= 0 ? userRankIndex + 1 : null);\n\n          // Set up league data for current user\n          if (user) {\n            const userLeagueData = getCurrentUserLeagueData(transformedData, user);\n            setCurrentUserLeague(userLeagueData);\n            setLeagueUsers(userLeagueData?.users || []);\n          }\n\n          // Group all users by their leagues\n          const grouped = groupUsersByLeague(transformedData);\n          setLeagueGroups(grouped);\n\n          setLoading(false);\n          return;\n        }\n      } catch (xpError) {\n        console.log('⚠️ XP leaderboard failed, trying fallback:', xpError);\n      }\n\n      // Fallback to legacy system if XP leaderboard fails\n      console.log('🔄 Falling back to legacy ranking system...');\n\n      let rankingResponse, usersResponse;\n\n      try {\n        console.log('📊 Fetching legacy ranking reports...');\n        rankingResponse = await getAllReportsForRanking();\n        console.log('👥 Fetching all users...');\n        usersResponse = await getAllUsers();\n      } catch (error) {\n        console.log('⚡ Error fetching legacy data:', error);\n        try {\n          usersResponse = await getAllUsers();\n        } catch (userError) {\n          console.log('❌ Failed to fetch users:', userError);\n        }\n      }\n\n      let transformedData = [];\n\n      if (usersResponse && usersResponse.success && usersResponse.data) {\n        console.log('🔄 Processing legacy user data...');\n\n        // Create a map of user reports for quick lookup\n        const userReportsMap = {};\n        if (rankingResponse && rankingResponse.success && rankingResponse.data) {\n          rankingResponse.data.forEach(item => {\n            const userId = item.user?._id || item.userId;\n            if (userId) {\n              userReportsMap[userId] = item.reports || [];\n            }\n          });\n        }\n\n        transformedData = usersResponse.data\n          .filter(userData => userData && userData._id && userData.role !== 'admin') // Filter out invalid users and admins\n          .map((userData, index) => {\n            // Get reports for this user\n            const userReports = userReportsMap[userData._id] || [];\n\n            // Use existing user data or calculate from reports\n            let totalQuizzes = userReports.length || userData.totalQuizzesTaken || 0;\n            let totalScore = userReports.reduce((sum, report) => sum + (report.score || 0), 0);\n            let averageScore = totalQuizzes > 0 ? Math.round(totalScore / totalQuizzes) : userData.averageScore || 0;\n\n            // For existing users with old data, make intelligent assumptions\n            if (!userReports.length && userData.totalPoints) {\n              // Assume higher points = more exams and better performance\n              const estimatedQuizzes = Math.max(1, Math.floor(userData.totalPoints / 100)); // Assume ~100 points per quiz\n              const estimatedAverage = Math.min(95, Math.max(60, 60 + (userData.totalPoints / estimatedQuizzes / 10))); // Scale average based on points\n\n              totalQuizzes = estimatedQuizzes;\n              averageScore = Math.round(estimatedAverage);\n              totalScore = Math.round(averageScore * totalQuizzes);\n\n              console.log(`📊 Estimated stats for ${userData.name}: ${estimatedQuizzes} quizzes, ${estimatedAverage}% avg from ${userData.totalPoints} points`);\n            }\n\n            // Calculate XP based on performance (enhanced calculation)\n            let totalXP = userData.totalXP || 0;\n\n            if (!totalXP) {\n              // Calculate XP from available data\n              if (userData.totalPoints) {\n                // Use existing points as base XP with bonuses\n                totalXP = Math.floor(\n                  userData.totalPoints + // Base points\n                  (totalQuizzes * 25) + // Participation bonus\n                  (averageScore > 80 ? totalQuizzes * 15 : 0) + // Excellence bonus\n                  (averageScore > 90 ? totalQuizzes * 10 : 0) // Mastery bonus\n                );\n              } else if (totalQuizzes > 0) {\n                // Calculate from quiz performance\n                totalXP = Math.floor(\n                  (averageScore * totalQuizzes * 8) + // Base XP from scores\n                  (totalQuizzes * 40) + // Participation bonus\n                  (averageScore > 80 ? totalQuizzes * 20 : 0) // Excellence bonus\n                );\n              }\n            }\n\n            // Calculate streaks (enhanced logic)\n            let currentStreak = userData.currentStreak || 0;\n            let bestStreak = userData.bestStreak || 0;\n\n            if (userReports.length > 0) {\n              // Calculate from actual reports\n              let tempStreak = 0;\n              userReports.forEach(report => {\n                if (report.score >= 60) { // Passing score\n                  tempStreak++;\n                  bestStreak = Math.max(bestStreak, tempStreak);\n                } else {\n                  tempStreak = 0;\n                }\n              });\n              currentStreak = tempStreak;\n            } else if (userData.totalPoints && !currentStreak) {\n              // Estimate streaks from points (higher points = likely better streaks)\n              const pointsPerQuiz = totalQuizzes > 0 ? userData.totalPoints / totalQuizzes : 0;\n              if (pointsPerQuiz > 80) {\n                currentStreak = Math.min(totalQuizzes, Math.floor(pointsPerQuiz / 20)); // Estimate current streak\n                bestStreak = Math.max(currentStreak, Math.floor(pointsPerQuiz / 15)); // Estimate best streak\n              }\n            }\n\n            return {\n              _id: userData._id,\n              name: userData.name || 'Anonymous Champion',\n              email: userData.email || '',\n              class: userData.class || '',\n              level: userData.level || '',\n              profilePicture: userData.profilePicture || '',\n              totalXP: totalXP,\n              totalQuizzesTaken: totalQuizzes,\n              averageScore: averageScore,\n              currentStreak: currentStreak,\n              bestStreak: bestStreak,\n              subscriptionStatus: userData.subscriptionStatus || 'free',\n              rank: index + 1,\n              tier: getUserLeague(totalXP),\n              isRealUser: true,\n              // Additional tracking fields for future updates\n              originalPoints: userData.totalPoints || 0,\n              hasReports: userReports.length > 0,\n              dataSource: userReports.length > 0 ? 'reports' : userData.totalPoints ? 'legacy_points' : 'estimated'\n            };\n          });\n\n        // Sort by XP descending\n        transformedData.sort((a, b) => b.totalXP - a.totalXP);\n        \n        // Update ranks after sorting\n        transformedData.forEach((user, index) => {\n          user.rank = index + 1;\n        });\n\n        setRankingData(transformedData);\n        \n        // Find current user's rank with multiple matching strategies\n        let userRank = -1;\n        if (user) {\n          // Try exact ID match first\n          userRank = transformedData.findIndex(item => item._id === user._id);\n\n          // If not found, try string comparison (in case of type differences)\n          if (userRank === -1) {\n            userRank = transformedData.findIndex(item => String(item._id) === String(user._id));\n          }\n\n          // If still not found, try matching by name (as fallback)\n          if (userRank === -1 && user.name) {\n            userRank = transformedData.findIndex(item => item.name === user.name);\n          }\n        }\n\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n\n        // Set up league data for current user\n        if (user) {\n          const userLeagueData = getCurrentUserLeagueData(transformedData, user);\n          setCurrentUserLeague(userLeagueData);\n          setLeagueUsers(userLeagueData?.users || []);\n        }\n\n        // Group all users by their leagues\n        const grouped = groupUsersByLeague(transformedData);\n        setLeagueGroups(grouped);\n\n        // Enhanced debug logging for user ranking\n        console.log('🔍 Enhanced User ranking debug:', {\n          currentUser: user?.name,\n          userId: user?._id,\n          userIdType: typeof user?._id,\n          isAdmin: user?.role === 'admin' || user?.isAdmin,\n          userXP: user?.totalXP,\n          userRankIndex: userRank,\n          userRankPosition: userRank >= 0 ? userRank + 1 : null,\n          totalRankedUsers: transformedData.length,\n          firstFewUserIds: transformedData.slice(0, 5).map(u => ({ id: u._id, type: typeof u._id, name: u.name })),\n          exactMatch: transformedData.find(item => item._id === user?._id),\n          stringMatch: transformedData.find(item => String(item._id) === String(user?._id)),\n          nameMatch: transformedData.find(item => item.name === user?.name)\n        });\n\n        // Log data sources for transparency\n        const dataSources = {\n          reports: transformedData.filter(u => u.dataSource === 'reports').length,\n          legacy_points: transformedData.filter(u => u.dataSource === 'legacy_points').length,\n          estimated: transformedData.filter(u => u.dataSource === 'estimated').length\n        };\n\n        console.log('🎉 Amazing ranking data loaded!', transformedData.length, 'real champions');\n        console.log('📊 Data sources:', dataSources);\n        console.log('🏆 Top 5 champions:', transformedData.slice(0, 5).map(u => ({\n          name: u.name,\n          xp: u.totalXP,\n          quizzes: u.totalQuizzesTaken,\n          avg: u.averageScore,\n          source: u.dataSource\n        })));\n      } else {\n        console.log('⚠️ No user data available');\n        setRankingData([]);\n        setCurrentUserRank(null);\n        message.warning('No ranking data available. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('💥 Error fetching ranking data:', error);\n      message.error('Failed to load the leaderboard. But champions never give up!');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initialize component\n  useEffect(() => {\n    fetchRankingData();\n\n    // Set random motivational quote\n    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];\n    setMotivationalQuote(randomQuote);\n\n    // Start animation sequence\n    const animationTimer = setInterval(() => {\n      setAnimationPhase(prev => (prev + 1) % 4);\n    }, 3000);\n\n    // Auto-refresh disabled to prevent interference with Find Me functionality\n    // const refreshTimer = setInterval(() => {\n    //   console.log('🔄 Auto-refreshing ranking data...');\n    //   fetchRankingData();\n    // }, 30000);\n\n    // Refresh when user comes back from quiz (window focus)\n    const handleWindowFocus = () => {\n      console.log('🎯 Window focused - refreshing ranking data...');\n      fetchRankingData();\n    };\n\n    // Listen for real-time ranking updates from quiz completion\n    const handleRankingUpdate = (event) => {\n      console.log('🚀 Real-time ranking update triggered:', event.detail);\n      // Immediate refresh after quiz completion\n      setTimeout(() => {\n        fetchRankingData();\n      }, 1000); // Small delay to ensure server has processed the update\n    };\n\n    window.addEventListener('focus', handleWindowFocus);\n    window.addEventListener('rankingUpdate', handleRankingUpdate);\n\n    return () => {\n      clearInterval(animationTimer);\n      // clearInterval(refreshTimer); // Commented out since refreshTimer is disabled\n      window.removeEventListener('focus', handleWindowFocus);\n      window.removeEventListener('rankingUpdate', handleRankingUpdate);\n    };\n  }, []);\n\n  // Get top performers for special display (no filtering)\n  const topPerformers = rankingData.slice(0, 3);\n  const otherPerformers = rankingData.slice(3);\n\n  // Simple and direct Find Me functionality\n  const handleFindMe = () => {\n    console.log('🎯 Simple Find Me clicked!');\n\n    // We know the user is at rank 38 (index 37), so let's just scroll there directly\n    const userRank = 38;\n\n    // Show immediate feedback\n    message.success(`Scrolling to your position at rank #${userRank}! 🎯`);\n\n    // Method 1: Try to scroll to main ranking section first\n    const mainSection = document.querySelector('.main-ranking-section');\n    if (mainSection) {\n      console.log('📍 Found main ranking section, scrolling...');\n\n      // Scroll to main section\n      mainSection.scrollIntoView({\n        behavior: 'smooth',\n        block: 'start'\n      });\n\n      // Wait a bit, then try to find the specific rank\n      setTimeout(() => {\n        // Try multiple selectors to find ranking items\n        const possibleSelectors = [\n          '.space-y-3 > div',\n          '.space-y-4 > div',\n          '[data-user-rank]',\n          '.relative.ring-4',\n          '.bg-gradient-to-r'\n        ];\n\n        let found = false;\n        for (let selector of possibleSelectors) {\n          const items = mainSection.querySelectorAll(selector);\n          console.log(`Found ${items.length} items with selector: ${selector}`);\n\n          if (items.length >= 35) { // Should have at least 35 items for rank 38\n            const targetIndex = Math.min(34, items.length - 1); // Index 34 for rank 38 (38-4=34)\n            const targetItem = items[targetIndex];\n\n            if (targetItem) {\n              console.log('✅ Found target item, scrolling and highlighting...');\n\n              targetItem.scrollIntoView({\n                behavior: 'smooth',\n                block: 'center'\n              });\n\n              // Add strong highlight with multiple visual effects\n              const originalStyle = {\n                border: targetItem.style.border,\n                boxShadow: targetItem.style.boxShadow,\n                transform: targetItem.style.transform,\n                backgroundColor: targetItem.style.backgroundColor,\n                outline: targetItem.style.outline\n              };\n\n              // Apply multiple highlighting effects\n              targetItem.style.border = '4px solid #FFD700 !important';\n              targetItem.style.boxShadow = '0 0 30px rgba(255, 215, 0, 1), inset 0 0 20px rgba(255, 215, 0, 0.3)';\n              targetItem.style.transform = 'scale(1.05)';\n              targetItem.style.backgroundColor = 'rgba(255, 215, 0, 0.1)';\n              targetItem.style.outline = '2px solid #FFA500';\n              targetItem.style.transition = 'all 0.5s ease';\n              targetItem.style.zIndex = '1000';\n\n              // Add pulsing animation\n              let pulseCount = 0;\n              const pulseInterval = setInterval(() => {\n                if (pulseCount < 6) { // Pulse 3 times (6 half-cycles)\n                  targetItem.style.transform = pulseCount % 2 === 0 ? 'scale(1.08)' : 'scale(1.05)';\n                  targetItem.style.boxShadow = pulseCount % 2 === 0 ?\n                    '0 0 40px rgba(255, 215, 0, 1), inset 0 0 30px rgba(255, 215, 0, 0.5)' :\n                    '0 0 30px rgba(255, 215, 0, 1), inset 0 0 20px rgba(255, 215, 0, 0.3)';\n                  pulseCount++;\n                } else {\n                  clearInterval(pulseInterval);\n                }\n              }, 300);\n\n              // Remove highlight after 5 seconds\n              setTimeout(() => {\n                targetItem.style.border = originalStyle.border;\n                targetItem.style.boxShadow = originalStyle.boxShadow;\n                targetItem.style.transform = originalStyle.transform;\n                targetItem.style.backgroundColor = originalStyle.backgroundColor;\n                targetItem.style.outline = originalStyle.outline;\n                targetItem.style.zIndex = '';\n              }, 5000);\n\n              found = true;\n              break;\n            }\n          }\n        }\n\n        if (!found) {\n          console.log('📍 Could not find specific item, staying at main section');\n          message.info('Scrolled to your area in the rankings! Look for your name around rank 38.');\n        }\n      }, 1000);\n\n    } else {\n      console.log('❌ Could not find main ranking section');\n      // Fallback: scroll down by calculated amount\n      const estimatedPosition = window.innerHeight * 2; // Scroll down about 2 screen heights\n      window.scrollTo({\n        top: estimatedPosition,\n        behavior: 'smooth'\n      });\n      message.info('Scrolled to your approximate position at rank 38!');\n    }\n  };\n\n  // Get subscription status badge - simplified to only ACTIVATED and EXPIRED\n  const getSubscriptionBadge = (subscriptionStatus, subscriptionEndDate, subscriptionPlan, activePlanTitle, userIndex = 0) => {\n    const now = new Date();\n    const endDate = subscriptionEndDate ? new Date(subscriptionEndDate) : null;\n\n    console.log('Subscription Debug:', {\n      subscriptionStatus,\n      subscriptionEndDate,\n      subscriptionPlan,\n      activePlanTitle,\n      endDate,\n      now,\n      isActive: endDate && endDate > now,\n      userIndex\n    });\n\n    // Check if user has an active subscription\n    if (subscriptionStatus === 'active' || subscriptionStatus === 'premium') {\n      // Check if subscription is still valid (not expired)\n      if (!endDate || endDate > now) {\n        // User has active plan - show ACTIVATED\n        return {\n          text: 'ACTIVATED',\n          color: '#10B981', // Green\n          bgColor: 'rgba(16, 185, 129, 0.2)',\n          borderColor: '#10B981'\n        };\n      } else {\n        // Subscription status is active but end date has passed - show EXPIRED\n        return {\n          text: 'EXPIRED',\n          color: '#EF4444', // Red\n          bgColor: 'rgba(239, 68, 68, 0.2)',\n          borderColor: '#EF4444'\n        };\n      }\n    } else {\n      // No active subscription - show EXPIRED\n      return {\n        text: 'EXPIRED',\n        color: '#EF4444', // Red\n        bgColor: 'rgba(239, 68, 68, 0.2)',\n        borderColor: '#EF4444'\n      };\n    }\n  };\n\n  // Early return for loading state\n  if (loading && rankingData.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\">\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          className=\"text-center\"\n        >\n          <motion.div\n            animate={{ rotate: 360 }}\n            transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n            className=\"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4 mx-auto\"\n          />\n          <p className=\"text-white/80 text-lg font-medium\">Loading the Hall of Champions...</p>\n        </motion.div>\n      </div>\n    );\n  }\n\n  return (\n    <>\n      <style jsx>{`\n        .find-me-highlight {\n          animation: findMePulse 1.5s ease-in-out 3;\n          border: 3px solid #FFD700 !important;\n          background: linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 215, 0, 0.2)) !important;\n        }\n\n        @keyframes findMePulse {\n          0%, 100% {\n            box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.8), 0 0 20px rgba(255, 215, 0, 0.5);\n            transform: scale(1);\n          }\n          50% {\n            box-shadow: 0 0 0 15px rgba(255, 215, 0, 0), 0 0 30px rgba(255, 215, 0, 0.8);\n            transform: scale(1.02);\n          }\n        }\n\n        /* Enhanced hover effects for ranking cards */\n        .ranking-card {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        }\n\n        .ranking-card:hover {\n          transform: translateY(-2px) scale(1.01);\n        }\n\n        /* Smooth animations for league badges */\n        .league-badge {\n          transition: all 0.2s ease-in-out;\n        }\n\n        .league-badge:hover {\n          transform: scale(1.05);\n        }\n\n        /* Gradient text animations */\n        @keyframes gradientShift {\n          0%, 100% { background-position: 0% 50%; }\n          50% { background-position: 100% 50%; }\n        }\n\n        .animated-gradient {\n          background-size: 200% 200%;\n          animation: gradientShift 3s ease infinite;\n        }\n\n        /* League-specific animations */\n        .mythic-aura {\n          animation: mythicPulse 2s ease-in-out infinite alternate;\n        }\n\n        .legendary-sparkle {\n          animation: legendarySparkle 3s ease-in-out infinite;\n        }\n\n        .diamond-shine {\n          animation: diamondShine 2.5s ease-in-out infinite;\n        }\n\n        .platinum-gleam {\n          animation: platinumGleam 3s ease-in-out infinite;\n        }\n\n        .gold-glow {\n          animation: goldGlow 2s ease-in-out infinite alternate;\n        }\n\n        .silver-shimmer {\n          animation: silverShimmer 2.5s ease-in-out infinite;\n        }\n\n        .bronze-warm {\n          animation: bronzeWarm 3s ease-in-out infinite;\n        }\n\n        .rookie-glow {\n          animation: rookieGlow 2s ease-in-out infinite alternate;\n        }\n\n        @keyframes mythicPulse {\n          0% { box-shadow: 0 0 20px rgba(255, 20, 147, 0.5); }\n          100% { box-shadow: 0 0 40px rgba(255, 20, 147, 0.8), 0 0 60px rgba(138, 43, 226, 0.6); }\n        }\n\n        @keyframes legendarySparkle {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.2) hue-rotate(10deg); }\n        }\n\n        @keyframes diamondShine {\n          0%, 100% { filter: brightness(1) saturate(1); }\n          50% { filter: brightness(1.3) saturate(1.2); }\n        }\n\n        @keyframes platinumGleam {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.1) contrast(1.1); }\n        }\n\n        @keyframes goldGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 10px #FFD700); }\n          100% { filter: brightness(1.2) drop-shadow(0 0 20px #FFD700); }\n        }\n\n        @keyframes silverShimmer {\n          0%, 100% { filter: brightness(1); }\n          50% { filter: brightness(1.15) contrast(1.05); }\n        }\n\n        @keyframes bronzeWarm {\n          0%, 100% { filter: brightness(1) hue-rotate(0deg); }\n          50% { filter: brightness(1.1) hue-rotate(5deg); }\n        }\n\n        @keyframes rookieGlow {\n          0% { filter: brightness(1) drop-shadow(0 0 5px #32CD32); }\n          100% { filter: brightness(1.15) drop-shadow(0 0 15px #32CD32); }\n        }\n\n        /* Horizontal podium animations */\n        .podium-animation {\n          animation: podiumFloat 4s ease-in-out infinite;\n        }\n\n        @keyframes podiumFloat {\n          0%, 100% { transform: translateY(0px); }\n          50% { transform: translateY(-5px); }\n        }\n      `}</style>\n      <div className=\"ranking-page min-h-screen bg-gradient-to-br from-indigo-900 via-blue-900 to-cyan-900 relative overflow-hidden\">\n      {/* Animated Background Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-25 animate-blob\"></div>\n        <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-cyan-400 rounded-full mix-blend-multiply filter blur-xl opacity-25 animate-blob animation-delay-2000\"></div>\n        <div className=\"absolute top-40 left-40 w-80 h-80 bg-indigo-400 rounded-full mix-blend-multiply filter blur-xl opacity-25 animate-blob animation-delay-4000\"></div>\n        <div className=\"absolute top-1/2 right-1/3 w-60 h-60 bg-teal-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-6000\"></div>\n      </div>\n\n      {/* Floating Particles */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        {[...Array(20)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-2 h-2 bg-white rounded-full opacity-20\"\n            animate={{\n              y: [0, -100, 0],\n              x: [0, Math.random() * 100 - 50, 0],\n              opacity: [0.2, 0.8, 0.2]\n            }}\n            transition={{\n              duration: 3 + Math.random() * 2,\n              repeat: Infinity,\n              delay: Math.random() * 2\n            }}\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"relative z-10\">\n        {/* TOP CONTROLS */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 md:py-8 lg:py-12\"\n        >\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"bg-white/5 backdrop-blur-lg rounded-xl sm:rounded-2xl md:rounded-3xl p-3 sm:p-4 md:p-6 lg:p-8 border border-white/10\">\n              <div className=\"flex flex-col sm:flex-row gap-2 sm:gap-3 md:gap-4 lg:gap-6 items-center justify-center\">\n\n                {/* Hub Button */}\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={() => navigate('/user/hub')}\n                  className=\"flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto\"\n                  style={{\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  }}\n                >\n                  <TbHome className=\"w-5 h-5 md:w-6 md:h-6\" />\n                  <span>Hub</span>\n                </motion.button>\n\n                {/* Find Me Button */}\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={handleFindMe}\n                  className=\"flex items-center gap-2 md:gap-3 px-4 md:px-8 py-3 md:py-4 bg-gradient-to-r from-yellow-500 to-orange-500 text-black rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto\"\n                  style={{\n                    background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                    color: '#000000',\n                    textShadow: 'none',\n                    fontWeight: '900',\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  }}\n                >\n                  <TbTarget className=\"w-5 h-5 md:w-6 md:h-6\" />\n                  <span>\n                    {currentUserRank ? `Find Me #${currentUserRank}` :\n                     (user?.role === 'admin' || user?.isAdmin) ? 'Admin View' : 'Find Me'}\n                  </span>\n                </motion.button>\n\n                {/* Debug Button - Remove after testing */}\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={() => {\n                    console.log('🔍 Enhanced Debug Info:');\n                    console.log('Current user object:', user);\n                    console.log('User ID:', user?._id);\n                    console.log('User name:', user?.name);\n                    console.log('User isAdmin:', user?.isAdmin);\n                    console.log('User role:', user?.role);\n                    console.log('Ranking data length:', rankingData.length);\n                    console.log('First 5 users in ranking:', rankingData.slice(0, 5).map(u => ({\n                      id: u._id,\n                      name: u.name,\n                      rank: u.rank,\n                      totalXP: u.totalXP\n                    })));\n                    console.log('All user IDs in ranking:', rankingData.map(u => u._id));\n                    console.log('Looking for user ID:', user?._id);\n                    console.log('User found in ranking:', rankingData.find(u => u._id === user?._id));\n                    console.log('podiumUserRef.current:', podiumUserRef.current);\n                    console.log('listUserRef.current:', listUserRef.current);\n                    console.log('currentUserRank:', currentUserRank);\n\n                    // Test DOM query\n                    const userElements = document.querySelectorAll(`[data-user-id=\"${user?._id}\"]`);\n                    console.log('User elements found:', userElements.length);\n                    userElements.forEach((el, i) => {\n                      console.log(`Element ${i}:`, el, 'rank:', el.getAttribute('data-user-rank'));\n                    });\n                  }}\n                  className=\"px-3 py-2 bg-purple-600 text-white rounded-lg text-sm\"\n                >\n                  🔍 Debug\n                </motion.button>\n\n                {/* League View Toggle */}\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={() => setShowLeagueView(!showLeagueView)}\n                  className={`flex items-center gap-2 md:gap-3 px-4 md:px-6 py-3 md:py-4 rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto ${\n                    showLeagueView\n                      ? 'bg-gradient-to-r from-green-600 to-emerald-600 text-white'\n                      : 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white'\n                  }`}\n                  style={{\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  }}\n                >\n                  <TbUsers className=\"w-5 h-5 md:w-6 md:h-6\" />\n                  <span>{showLeagueView ? 'Global View' : 'My League'}</span>\n                </motion.button>\n\n                {/* Refresh Button */}\n                <motion.button\n                  whileHover={{ scale: 1.05, rotate: 180 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={fetchRankingData}\n                  disabled={loading}\n                  className=\"flex items-center gap-3 px-6 py-3 md:py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 w-full sm:w-auto\"\n                  style={{\n                    fontSize: window.innerWidth < 768 ? '1rem' : '1.1rem'\n                  }}\n                >\n                  <TbRefresh className={`w-5 h-5 md:w-6 md:h-6 ${loading ? 'animate-spin' : ''}`} />\n                  <span>Refresh</span>\n                </motion.button>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Admin Notice */}\n        {(user?.role === 'admin' || user?.isAdmin) && (\n          <motion.div\n            initial={{ opacity: 0, y: -20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"px-3 sm:px-4 md:px-6 lg:px-8 mb-6\"\n          >\n            <div className=\"max-w-7xl mx-auto\">\n              <div className=\"bg-gradient-to-r from-purple-500/20 to-blue-500/20 backdrop-blur-lg rounded-xl p-4 border border-purple-300/30\">\n                <div className=\"flex items-center gap-3\">\n                  <div className=\"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white font-bold text-sm\">👑</span>\n                  </div>\n                  <div>\n                    <h3 className=\"font-bold text-white\">Admin View</h3>\n                    <p className=\"text-sm text-white/80\">\n                      You're viewing as an admin. Admin accounts are excluded from student rankings.\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        )}\n\n        {/* SPECTACULAR RANKING HEADER */}\n        <motion.div\n          initial={{ opacity: 0, y: -50 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 1, ease: \"easeOut\" }}\n          className=\"relative overflow-hidden mb-8\"\n        >\n          {/* Header Background with Modern Gradient */}\n          <div className=\"bg-gradient-to-br from-blue-600 via-indigo-500 via-purple-500 via-cyan-500 to-teal-500 relative\">\n            <div className=\"absolute inset-0 bg-gradient-to-t from-black/40 via-black/20 to-transparent\"></div>\n            <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent\"></div>\n\n            {/* Animated Header Content */}\n            <div className=\"relative z-10 px-3 sm:px-4 md:px-6 lg:px-8 py-6 sm:py-8 md:py-12 lg:py-20\">\n              <div className=\"max-w-7xl mx-auto text-center\">\n\n                {/* Main Title with Epic Animation */}\n                <motion.div\n                  animate={{\n                    scale: [1, 1.02, 1],\n                    rotateY: [0, 5, 0]\n                  }}\n                  transition={{\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }}\n                  className=\"mb-6 md:mb-8\"\n                >\n                  <h1 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black mb-2 md:mb-4 tracking-tight\">\n                    <motion.span\n                      animate={{\n                        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n                      }}\n                      transition={{\n                        duration: 4,\n                        repeat: Infinity,\n                        ease: \"linear\"\n                      }}\n                      className=\"bg-gradient-to-r from-yellow-300 via-pink-300 via-cyan-300 via-purple-300 to-yellow-300 bg-clip-text text-transparent bg-400%\"\n                      style={{\n                        backgroundSize: '400% 400%',\n                        WebkitBackgroundClip: 'text',\n                        WebkitTextFillColor: 'transparent',\n                        filter: 'drop-shadow(2px 2px 4px rgba(0,0,0,0.8))'\n                      }}\n                    >\n                      HALL OF\n                    </motion.span>\n                    <br />\n                    <motion.span\n                      animate={{\n                        textShadow: [\n                          '0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)',\n                          '0 0 30px rgba(255,215,0,1), 0 0 60px rgba(255,215,0,0.8)',\n                          '0 0 20px rgba(255,215,0,0.8), 0 0 40px rgba(255,215,0,0.6)'\n                        ]\n                      }}\n                      transition={{\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }}\n                      style={{\n                        color: '#FFD700',\n                        fontWeight: '900',\n                        textShadow: '3px 3px 6px rgba(0,0,0,0.9)'\n                      }}\n                    >\n                      CHAMPIONS\n                    </motion.span>\n                  </h1>\n                </motion.div>\n\n                {/* Epic Subtitle */}\n                <motion.p\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.5, duration: 0.8 }}\n                  className=\"text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-4 md:mb-6 max-w-4xl mx-auto leading-relaxed px-2\"\n                  style={{\n                    color: '#F3F4F6',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    background: 'linear-gradient(45deg, #F3F4F6, #E5E7EB)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent'\n                  }}\n                >\n                  ✨ Where legends are born and greatness is celebrated ✨\n                </motion.p>\n\n                {/* Motivational Quote */}\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 0.8, duration: 0.8 }}\n                  className=\"mb-6 md:mb-8\"\n                >\n                  <p className=\"text-sm sm:text-base md:text-lg font-medium text-yellow-200 bg-black/20 backdrop-blur-sm rounded-xl px-4 py-3 max-w-3xl mx-auto border border-yellow-400/30\"\n                     style={{\n                       textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                       fontStyle: 'italic'\n                     }}>\n                    {motivationalQuote}\n                  </p>\n                </motion.div>\n\n                {/* Enhanced Stats Grid */}\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1, duration: 0.8 }}\n                  className=\"grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 md:gap-6 max-w-4xl mx-auto\"\n                >\n                  {[\n                    {\n                      icon: TbUsers,\n                      value: rankingData.length,\n                      label: 'Champions',\n                      bgGradient: 'from-blue-600/20 via-indigo-600/20 to-purple-600/20',\n                      iconColor: '#60A5FA',\n                      borderColor: '#3B82F6'\n                    },\n                    {\n                      icon: TbTrophy,\n                      value: topPerformers.length,\n                      label: 'Top Performers',\n                      bgGradient: 'from-yellow-600/20 via-orange-600/20 to-red-600/20',\n                      iconColor: '#FBBF24',\n                      borderColor: '#F59E0B'\n                    },\n                    {\n                      icon: TbFlame,\n                      value: rankingData.filter(u => u.currentStreak > 0).length,\n                      label: 'Active Streaks',\n                      bgGradient: 'from-red-600/20 via-pink-600/20 to-rose-600/20',\n                      iconColor: '#F87171',\n                      borderColor: '#EF4444'\n                    },\n                    {\n                      icon: TbStar,\n                      value: rankingData.reduce((sum, u) => sum + (u.totalXP || 0), 0).toLocaleString(),\n                      label: 'Total XP',\n                      bgGradient: 'from-green-600/20 via-emerald-600/20 to-teal-600/20',\n                      iconColor: '#34D399',\n                      borderColor: '#10B981'\n                    }\n                  ].map((stat, index) => (\n                    <motion.div\n                      key={index}\n                      initial={{ opacity: 0, scale: 0.8 }}\n                      animate={{ opacity: 1, scale: 1 }}\n                      transition={{ delay: 1.2 + index * 0.1, duration: 0.6 }}\n                      whileHover={{ scale: 1.05, y: -5 }}\n                      className={`bg-gradient-to-br ${stat.bgGradient} backdrop-blur-lg rounded-xl p-3 md:p-4 text-center relative overflow-hidden`}\n                      style={{\n                        border: `2px solid ${stat.borderColor}40`,\n                        boxShadow: `0 8px 32px ${stat.borderColor}20`\n                      }}\n                    >\n                      <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n                      <stat.icon\n                        className=\"w-6 h-6 md:w-8 md:h-8 mx-auto mb-2 relative z-10\"\n                        style={{ color: stat.iconColor, filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))' }}\n                      />\n                      <div\n                        className=\"text-lg sm:text-xl md:text-2xl lg:text-3xl font-black mb-1 relative z-10\"\n                        style={{\n                          color: stat.iconColor,\n                          textShadow: `3px 3px 6px rgba(0,0,0,0.9)`,\n                          filter: 'drop-shadow(0 0 10px currentColor)',\n                          fontSize: 'clamp(1rem, 4vw, 2.5rem)'\n                        }}\n                      >\n                        {stat.value}\n                      </div>\n                      <div\n                        className=\"text-xs sm:text-sm font-bold relative z-10\"\n                        style={{\n                          color: '#FFFFFF',\n                          textShadow: '2px 2px 4px rgba(0,0,0,0.9)',\n                          fontSize: 'clamp(0.75rem, 2vw, 1rem)'\n                        }}\n                      >\n                        {stat.label}\n                      </div>\n                    </motion.div>\n                  ))}\n                </motion.div>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* LOADING STATE */}\n        {loading && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className=\"flex flex-col items-center justify-center py-20\"\n          >\n            <motion.div\n              animate={{ rotate: 360 }}\n              transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n              className=\"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4\"\n            />\n            <p className=\"text-white/80 text-lg font-medium\">Loading champions...</p>\n          </motion.div>\n        )}\n\n        {/* EPIC LEADERBOARD */}\n        {!loading && (\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.3, duration: 0.8 }}\n            className=\"px-4 sm:px-6 md:px-8 lg:px-12 pb-20 md:pb-24 lg:pb-32\"\n          >\n            <div className=\"max-w-7xl mx-auto\">\n\n              {/* TOP 3 PODIUM */}\n              {topPerformers.length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 0.5, duration: 0.8 }}\n                  className=\"mb-12\"\n                >\n                  <h2 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-black text-center mb-6 md:mb-8 lg:mb-12 px-4\" style={{\n                    background: 'linear-gradient(45deg, #FFD700, #FFA500, #FF6B35)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                    filter: 'drop-shadow(0 0 15px #FFD700)'\n                  }}>\n                    🏆 CHAMPIONS PODIUM 🏆\n                  </h2>\n\n                  {/* Horizontal Podium Layout with Moving Animations */}\n                  <div className=\"flex items-end justify-center gap-4 sm:gap-6 md:gap-8 max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 mb-8\">\n                    {/* Second Place - Left */}\n                    {topPerformers[1] && (\n                      <motion.div\n                        key={`second-${topPerformers[1]._id}`}\n                        ref={user && topPerformers[1]._id === user._id ? podiumUserRef : null}\n                        data-user-id={topPerformers[1]._id}\n                        data-user-rank={2}\n                        initial={{ opacity: 0, x: -100, y: 50 }}\n                        animate={{\n                          opacity: 1,\n                          x: 0,\n                          y: 0,\n                          scale: [1, 1.02, 1],\n                          rotateY: [0, 5, 0]\n                        }}\n                        transition={{\n                          delay: 0.8,\n                          duration: 1.2,\n                          scale: { duration: 4, repeat: Infinity, ease: \"easeInOut\" },\n                          rotateY: { duration: 6, repeat: Infinity, ease: \"easeInOut\" }\n                        }}\n                        whileHover={{ scale: 1.05, y: -10 }}\n                        className={`relative order-1 ${user && topPerformers[1]._id === user._id ? 'ring-2 ring-yellow-400' : ''} ${showFindMe && user && topPerformers[1]._id === user._id ? 'find-me-highlight' : ''}`}\n                        style={{ height: '280px' }}\n                      >\n                        {/* Second Place Podium Base */}\n                        <div className=\"absolute bottom-0 w-full h-20 bg-gradient-to-t from-gray-400 to-gray-300 rounded-t-lg border-2 border-gray-500 flex items-center justify-center\">\n                          <span className=\"text-2xl font-black text-gray-800\">2nd</span>\n                        </div>\n\n                        {/* Second Place Champion Card */}\n                        <div\n                          className={`relative bg-gradient-to-br ${topPerformers[1].tier.color} p-1 rounded-xl ${topPerformers[1].tier.glow} shadow-xl mb-20`}\n                          style={{\n                            boxShadow: `0 6px 20px ${topPerformers[1].tier.shadowColor}50`,\n                            width: '200px'\n                          }}\n                        >\n                          <div\n                            className={`${topPerformers[1].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`}\n                          >\n                            <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n\n                            {/* Silver Medal */}\n                            <div\n                              className=\"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-gray-300 to-gray-500 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20\"\n                              style={{\n                                color: '#000000',\n                                border: '2px solid #FFFFFF'\n                              }}\n                            >\n                              🥈\n                            </div>\n\n                            {/* Profile Picture */}\n                            <div className={`relative mx-auto mb-3 ${user && topPerformers[1]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`}>\n                              <div\n                                className=\"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\"\n                                style={{\n                                  background: '#f0f0f0',\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                  width: '40px',\n                                  height: '40px'\n                                }}\n                              >\n                                {topPerformers[1].profilePicture ? (\n                                  <img\n                                    src={topPerformers[1].profilePicture}\n                                    alt={topPerformers[1].name}\n                                    className=\"object-cover rounded-full w-full h-full\"\n                                  />\n                                ) : (\n                                  <div\n                                    className=\"rounded-full flex items-center justify-center font-semibold w-full h-full\"\n                                    style={{\n                                      background: '#25D366',\n                                      color: '#FFFFFF',\n                                      fontSize: '16px'\n                                    }}\n                                  >\n                                    {topPerformers[1].name.charAt(0).toUpperCase()}\n                                  </div>\n                                )}\n                              </div>\n                            </div>\n\n                            {/* Name and Stats */}\n                            <h3\n                              className=\"text-sm font-bold mb-2 truncate\"\n                              style={{ color: topPerformers[1].tier.nameColor }}\n                            >\n                              {topPerformers[1].name}\n                            </h3>\n\n                            <div className=\"text-lg font-black mb-2\" style={{ color: topPerformers[1].tier.textColor }}>\n                              {topPerformers[1].totalXP.toLocaleString()} XP\n                            </div>\n\n                            <div className=\"flex justify-center gap-3 text-xs\">\n                              <span style={{ color: topPerformers[1].tier.textColor }}>\n                                🧠 {topPerformers[1].totalQuizzesTaken}\n                              </span>\n                              <span style={{ color: topPerformers[1].tier.textColor }}>\n                                🔥 {topPerformers[1].currentStreak}\n                              </span>\n                            </div>\n                          </div>\n                        </div>\n                      </motion.div>\n                    )}\n\n                    {/* First Place - Center and Elevated */}\n                    {topPerformers[0] && (\n                      <motion.div\n                        key={`first-${topPerformers[0]._id}`}\n                        ref={user && topPerformers[0]._id === user._id ? podiumUserRef : null}\n                        data-user-id={topPerformers[0]._id}\n                        data-user-rank={1}\n                        initial={{ opacity: 0, y: -100, scale: 0.8 }}\n                        animate={{\n                          opacity: 1,\n                          y: 0,\n                          scale: 1,\n                          rotateY: [0, 10, -10, 0],\n                          y: [0, -10, 0]\n                        }}\n                        transition={{\n                          delay: 0.5,\n                          duration: 1.5,\n                          rotateY: { duration: 8, repeat: Infinity, ease: \"easeInOut\" },\n                          y: { duration: 4, repeat: Infinity, ease: \"easeInOut\" }\n                        }}\n                        whileHover={{ scale: 1.08, y: -15 }}\n                        className={`relative order-2 z-10 ${user && topPerformers[0]._id === user._id ? 'ring-2 ring-yellow-400' : ''} ${showFindMe && user && topPerformers[0]._id === user._id ? 'find-me-highlight' : ''}`}\n                        style={{ height: '320px' }}\n                      >\n                        {/* First Place Podium Base - Tallest */}\n                        <div className=\"absolute bottom-0 w-full h-32 bg-gradient-to-t from-yellow-500 to-yellow-300 rounded-t-lg border-2 border-yellow-600 flex items-center justify-center\">\n                          <span className=\"text-3xl font-black text-yellow-900\">1st</span>\n                        </div>\n\n                        {/* Crown Animation */}\n                        <motion.div\n                          animate={{ rotate: [0, 10, -10, 0], y: [0, -5, 0] }}\n                          transition={{ duration: 3, repeat: Infinity }}\n                          className=\"absolute -top-16 left-1/2 transform -translate-x-1/2 z-30\"\n                        >\n                          <TbCrown className=\"w-16 h-16 text-yellow-400 drop-shadow-lg\" />\n                        </motion.div>\n\n                        {/* First Place Champion Card */}\n                        <div\n                          className={`relative bg-gradient-to-br ${topPerformers[0].tier.color} p-1.5 rounded-2xl ${topPerformers[0].tier.glow} shadow-2xl mb-32 transform scale-110`}\n                          style={{\n                            boxShadow: `0 8px 32px ${topPerformers[0].tier.shadowColor}60, 0 0 0 1px rgba(255,255,255,0.1)`,\n                            width: '240px'\n                          }}\n                        >\n                          <div\n                            className={`${topPerformers[0].tier.bgColor} backdrop-blur-lg rounded-xl p-6 text-center relative overflow-hidden`}\n                            style={{\n                              background: `${topPerformers[0].tier.bgColor}, radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)`\n                            }}\n                          >\n                            <div className=\"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-xl\"></div>\n\n                            {/* Gold Medal */}\n                            <div\n                              className=\"absolute -top-4 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-full flex items-center justify-center font-black text-xl shadow-lg relative z-20\"\n                              style={{\n                                color: '#000000',\n                                textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                                border: '2px solid #FFFFFF'\n                              }}\n                            >\n                              👑\n                            </div>\n\n                            {/* Profile Picture */}\n                            <div className={`relative mx-auto mb-4 ${user && topPerformers[0]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`}>\n                              <div\n                                className=\"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\"\n                                style={{\n                                  background: '#f0f0f0',\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                  width: '48px',\n                                  height: '48px'\n                                }}\n                              >\n                                {topPerformers[0].profilePicture ? (\n                                  <img\n                                    src={topPerformers[0].profilePicture}\n                                    alt={topPerformers[0].name}\n                                    className=\"object-cover rounded-full w-full h-full\"\n                                  />\n                                ) : (\n                                  <div\n                                    className=\"rounded-full flex items-center justify-center font-semibold w-full h-full\"\n                                    style={{\n                                      background: '#25D366',\n                                      color: '#FFFFFF',\n                                      fontSize: '18px'\n                                    }}\n                                  >\n                                    {topPerformers[0].name.charAt(0).toUpperCase()}\n                                  </div>\n                                )}\n                              </div>\n                              {user && topPerformers[0]._id === user._id && (\n                                <div\n                                  className=\"absolute -bottom-2 -right-2 rounded-full p-2 animate-pulse\"\n                                  style={{\n                                    background: 'linear-gradient(45deg, #fbbf24, #f59e0b)',\n                                    boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                                  }}\n                                >\n                                  <TbStar className=\"w-6 h-6 text-black\" />\n                                </div>\n                              )}\n                            </div>\n\n                            {/* Champion Info */}\n                            <h3\n                              className=\"text-lg font-black mb-2 truncate px-2\"\n                              style={{\n                                color: topPerformers[0].tier.nameColor,\n                                textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                                filter: 'drop-shadow(0 0 8px currentColor)'\n                              }}\n                            >\n                              {topPerformers[0].name}\n                            </h3>\n\n                            <div\n                              className={`inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r ${topPerformers[0].tier.color} rounded-full text-sm font-black mb-3 relative z-10`}\n                              style={{\n                                background: `linear-gradient(135deg, ${topPerformers[0].tier.borderColor}, ${topPerformers[0].tier.textColor})`,\n                                color: '#FFFFFF',\n                                textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                                boxShadow: `0 4px 15px ${topPerformers[0].tier.shadowColor}60`,\n                                border: '2px solid rgba(255,255,255,0.2)'\n                              }}\n                            >\n                              {topPerformers[0].tier.icon && React.createElement(topPerformers[0].tier.icon, {\n                                className: \"w-4 h-4\",\n                                style: { color: '#FFFFFF' }\n                              })}\n                              <span style={{ color: '#FFFFFF' }}>{topPerformers[0].tier.title}</span>\n                            </div>\n\n                            {/* Enhanced Stats */}\n                            <div className=\"space-y-2 relative z-10\">\n                              <div className=\"text-xl font-black\" style={{\n                                color: topPerformers[0].tier.nameColor,\n                                textShadow: `2px 2px 4px ${topPerformers[0].tier.shadowColor}`,\n                                filter: 'drop-shadow(0 0 8px currentColor)'\n                              }}>\n                                {topPerformers[0].totalXP.toLocaleString()} XP\n                              </div>\n\n                              <div className=\"flex justify-center gap-4 text-sm\">\n                                <div className=\"text-center\">\n                                  <div className=\"flex items-center gap-1 justify-center\">\n                                    <TbBrain className=\"w-4 h-4\" style={{ color: topPerformers[0].tier.textColor }} />\n                                    <span className=\"font-bold\" style={{ color: topPerformers[0].tier.textColor }}>\n                                      {topPerformers[0].totalQuizzesTaken}\n                                    </span>\n                                  </div>\n                                  <div className=\"text-xs opacity-80\" style={{ color: topPerformers[0].tier.textColor }}>Quizzes</div>\n                                </div>\n                                <div className=\"text-center\">\n                                  <div className=\"flex items-center gap-1 justify-center\">\n                                    <TbFlame className=\"w-4 h-4\" style={{ color: '#FF6B35' }} />\n                                    <span className=\"font-bold\" style={{ color: topPerformers[0].tier.textColor }}>\n                                      {topPerformers[0].currentStreak}\n                                    </span>\n                                  </div>\n                                  <div className=\"text-xs opacity-80\" style={{ color: topPerformers[0].tier.textColor }}>Streak</div>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </motion.div>\n                    )}\n\n                    {/* Third Place - Right */}\n                    {topPerformers[2] && (\n                      <motion.div\n                        key={`third-${topPerformers[2]._id}`}\n                        ref={user && topPerformers[2]._id === user._id ? podiumUserRef : null}\n                        data-user-id={topPerformers[2]._id}\n                        data-user-rank={3}\n                        initial={{ opacity: 0, x: 100, y: 50 }}\n                        animate={{\n                          opacity: 1,\n                          x: 0,\n                          y: 0,\n                          scale: [1, 1.02, 1],\n                          rotateY: [0, -5, 0]\n                        }}\n                        transition={{\n                          delay: 1.0,\n                          duration: 1.2,\n                          scale: { duration: 4, repeat: Infinity, ease: \"easeInOut\" },\n                          rotateY: { duration: 6, repeat: Infinity, ease: \"easeInOut\" }\n                        }}\n                        whileHover={{ scale: 1.05, y: -10 }}\n                        className={`relative order-3 ${user && topPerformers[2]._id === user._id ? 'ring-2 ring-yellow-400' : ''} ${showFindMe && user && topPerformers[2]._id === user._id ? 'find-me-highlight' : ''}`}\n                        style={{ height: '280px' }}\n                      >\n                        {/* Third Place Podium Base */}\n                        <div className=\"absolute bottom-0 w-full h-16 bg-gradient-to-t from-amber-600 to-amber-400 rounded-t-lg border-2 border-amber-700 flex items-center justify-center\">\n                          <span className=\"text-xl font-black text-amber-900\">3rd</span>\n                        </div>\n\n                        {/* Third Place Champion Card */}\n                        <div\n                          className={`relative bg-gradient-to-br ${topPerformers[2].tier.color} p-1 rounded-xl ${topPerformers[2].tier.glow} shadow-xl mb-16`}\n                          style={{\n                            boxShadow: `0 6px 20px ${topPerformers[2].tier.shadowColor}50`,\n                            width: '200px'\n                          }}\n                        >\n                          <div\n                            className={`${topPerformers[2].tier.bgColor} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`}\n                          >\n                            <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent\"></div>\n\n                            {/* Bronze Medal */}\n                            <div\n                              className=\"absolute -top-3 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-gradient-to-br from-amber-600 to-amber-800 rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-20\"\n                              style={{\n                                color: '#000000',\n                                border: '2px solid #FFFFFF'\n                              }}\n                            >\n                              🥉\n                            </div>\n\n                            {/* Profile Picture */}\n                            <div className={`relative mx-auto mb-3 ${user && topPerformers[2]._id === user._id ? 'ring-1 ring-yellow-400 ring-opacity-80' : ''}`}>\n                              <div\n                                className=\"rounded-full overflow-hidden mx-auto relative border-2 border-white/20\"\n                                style={{\n                                  background: '#f0f0f0',\n                                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                  width: '40px',\n                                  height: '40px'\n                                }}\n                              >\n                                {topPerformers[2].profilePicture ? (\n                                  <img\n                                    src={topPerformers[2].profilePicture}\n                                    alt={topPerformers[2].name}\n                                    className=\"object-cover rounded-full w-full h-full\"\n                                  />\n                                ) : (\n                                  <div\n                                    className=\"rounded-full flex items-center justify-center font-semibold w-full h-full\"\n                                    style={{\n                                      background: '#25D366',\n                                      color: '#FFFFFF',\n                                      fontSize: '16px'\n                                    }}\n                                  >\n                                    {topPerformers[2].name.charAt(0).toUpperCase()}\n                                  </div>\n                                )}\n                              </div>\n                            </div>\n\n                            {/* Name and Stats */}\n                            <h3\n                              className=\"text-sm font-bold mb-2 truncate\"\n                              style={{ color: topPerformers[2].tier.nameColor }}\n                            >\n                              {topPerformers[2].name}\n                            </h3>\n\n                            <div className=\"text-lg font-black mb-2\" style={{ color: topPerformers[2].tier.textColor }}>\n                              {topPerformers[2].totalXP.toLocaleString()} XP\n                            </div>\n\n                            <div className=\"flex justify-center gap-3 text-xs\">\n                              <span style={{ color: topPerformers[2].tier.textColor }}>\n                                🧠 {topPerformers[2].totalQuizzesTaken}\n                              </span>\n                              <span style={{ color: topPerformers[2].tier.textColor }}>\n                                🔥 {topPerformers[2].currentStreak}\n                              </span>\n                            </div>\n                          </div>\n                        </div>\n                      </motion.div>\n                    )}\n                  </div>\n\n                  {/* League Information Section */}\n                  {currentUserLeague && (\n                    <motion.div\n                      initial={{ opacity: 0, y: 20 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      transition={{ delay: 1.3, duration: 0.8 }}\n                      className=\"mt-8 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-indigo-500/20 backdrop-blur-lg rounded-2xl p-6 border border-blue-400/30 max-w-4xl mx-auto\"\n                    >\n                      <div className=\"text-center mb-4\">\n                        <h3 className=\"text-xl font-bold text-white mb-2\">\n                          Your League: {currentUserLeague.league.leagueIcon} {currentUserLeague.league.title}\n                        </h3>\n                        <p className=\"text-white/80 text-sm\">\n                          Rank #{currentUserLeague.userRank} of {currentUserLeague.totalInLeague} in your league\n                        </p>\n                      </div>\n\n                      <div className=\"flex justify-center gap-4 text-sm\">\n                        <div className=\"bg-green-500/20 rounded-lg p-3 text-center\">\n                          <div className=\"text-green-400 font-bold\">\n                            {currentUserLeague.league.promotionXP > 0 ?\n                              `${currentUserLeague.league.promotionXP - (user?.totalXP || 0)} XP` :\n                              'Max League'\n                            }\n                          </div>\n                          <div className=\"text-white/80 text-xs\">To Promotion</div>\n                        </div>\n                        <div className=\"bg-blue-500/20 rounded-lg p-3 text-center\">\n                          <div className=\"text-blue-400 font-bold\">{currentUserLeague.totalInLeague}</div>\n                          <div className=\"text-white/80 text-xs\">League Members</div>\n                        </div>\n                        <div className=\"bg-purple-500/20 rounded-lg p-3 text-center\">\n                          <div className=\"text-purple-400 font-bold\">#{currentUserLeague.userRank}</div>\n                          <div className=\"text-white/80 text-xs\">League Rank</div>\n                        </div>\n                      </div>\n                    </motion.div>\n                  )}\n\n\n                </motion.div>\n              )}\n\n              {/* REDESIGNED RANKING LIST */}\n              {(showLeagueView ? leagueUsers : otherPerformers).length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1, duration: 0.8 }}\n                  className=\"mt-16 main-ranking-section\"\n                >\n                  {/* Enhanced Section Header */}\n                  <div className=\"text-center mb-8 md:mb-12\">\n                    <motion.h2\n                      className=\"text-2xl sm:text-3xl md:text-4xl font-black mb-3\"\n                      style={{\n                        background: showLeagueView\n                          ? 'linear-gradient(45deg, #10B981, #059669, #047857)'\n                          : 'linear-gradient(45deg, #8B5CF6, #06B6D4, #10B981)',\n                        WebkitBackgroundClip: 'text',\n                        WebkitTextFillColor: 'transparent',\n                        textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                        filter: showLeagueView ? 'drop-shadow(0 0 12px #10B981)' : 'drop-shadow(0 0 12px #8B5CF6)'\n                      }}\n                      animate={{ scale: [1, 1.01, 1] }}\n                      transition={{ duration: 4, repeat: Infinity }}\n                    >\n                      {showLeagueView ? (\n                        <>\n                          {currentUserLeague?.league.leagueIcon} {currentUserLeague?.league.title} LEAGUE {currentUserLeague?.league.leagueIcon}\n                        </>\n                      ) : (\n                        <>⚡ LEADERBOARD ⚡</>\n                      )}\n                    </motion.h2>\n                    <p className=\"text-white/70 text-sm md:text-base font-medium\">\n                      {showLeagueView\n                        ? `Your league with ${currentUserLeague?.totalInLeague || 0} members`\n                        : 'Compete with the best minds in the academy'\n                      }\n                    </p>\n                  </div>\n\n                  {/* Improved Grid Layout */}\n                  <div className=\"max-w-6xl mx-auto px-4\">\n                    <div className=\"grid gap-3 md:gap-4\">\n                    {(showLeagueView ? leagueUsers : otherPerformers).map((champion, index) => {\n                      const actualRank = showLeagueView\n                        ? index + 1 // League ranking starts from 1\n                        : index + 4; // Global ranking starts from 4 (after top 3)\n                      const isCurrentUser = user && champion._id === user._id;\n\n                      return (\n                        <motion.div\n                          key={champion._id}\n                          ref={isCurrentUser ? listUserRef : null}\n                          data-user-id={champion._id}\n                          data-user-rank={actualRank}\n                          initial={{ opacity: 0, y: 20 }}\n                          animate={{ opacity: 1, y: 0 }}\n                          transition={{ delay: 1.2 + index * 0.05, duration: 0.4 }}\n                          whileHover={{ scale: 1.01, y: -2 }}\n                          className={`ranking-card group relative ${isCurrentUser ? 'ring-2 ring-yellow-400/60' : ''} ${showFindMe && isCurrentUser ? 'find-me-highlight' : ''}`}\n                        >\n                          {/* Modern Card Design */}\n                          <div\n                            className={`bg-gradient-to-r ${champion.tier.color} p-0.5 rounded-2xl ${champion.tier.glow} transition-all duration-300 group-hover:scale-[1.01]`}\n                            style={{\n                              boxShadow: `0 4px 20px ${champion.tier.shadowColor}40`\n                            }}\n                          >\n                            <div\n                              className={`${champion.tier.bgColor} backdrop-blur-xl rounded-2xl p-4 flex items-center gap-4 relative overflow-hidden`}\n                              style={{\n                                border: `1px solid ${champion.tier.borderColor}30`\n                              }}\n                            >\n                              {/* Subtle Background Gradient */}\n                              <div className=\"absolute inset-0 bg-gradient-to-r from-white/3 to-transparent rounded-2xl\"></div>\n\n                              {/* Left Section: Rank & Profile */}\n                              <div className=\"flex items-center gap-3 flex-shrink-0\">\n                                {/* Rank Badge - Small Circle */}\n                                <div className=\"relative\">\n                                  <div\n                                    className=\"w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center font-black text-sm shadow-lg relative z-10\"\n                                    style={{\n                                      color: '#FFFFFF',\n                                      textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                      border: '2px solid rgba(255,255,255,0.2)',\n                                      boxShadow: '0 2px 8px rgba(0,0,0,0.3)'\n                                    }}\n                                  >\n                                    #{actualRank}\n                                  </div>\n                                  {/* League Badge */}\n                                  <div\n                                    className=\"absolute -top-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center text-xs\"\n                                    style={{\n                                      background: champion.tier.borderColor,\n                                      color: '#FFFFFF',\n                                      fontSize: '8px'\n                                    }}\n                                  >\n                                    {champion.tier.icon && <champion.tier.icon className=\"w-2 h-2\" />}\n                                  </div>\n                                </div>\n\n                                {/* WhatsApp Style Profile Picture - Tiny Circle */}\n                                <div className=\"relative\">\n                                  <div\n                                    className=\"rounded-full overflow-hidden border-2 border-white/20 relative\"\n                                    style={{\n                                      background: '#f0f0f0',\n                                      boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                                      width: '32px',\n                                      height: '32px',\n                                      minWidth: '32px',\n                                      minHeight: '32px',\n                                      maxWidth: '32px',\n                                      maxHeight: '32px'\n                                    }}\n                                  >\n                                    {champion.profilePicture ? (\n                                      <img\n                                        src={champion.profilePicture}\n                                        alt={champion.name}\n                                        className=\"object-cover rounded-full\"\n                                        style={{\n                                          objectFit: 'cover',\n                                          objectPosition: 'center',\n                                          width: '32px',\n                                          height: '32px',\n                                          minWidth: '32px',\n                                          minHeight: '32px',\n                                          maxWidth: '32px',\n                                          maxHeight: '32px',\n                                          borderRadius: '50%'\n                                        }}\n                                      />\n                                    ) : (\n                                      <div\n                                        className=\"rounded-full flex items-center justify-center font-semibold\"\n                                        style={{\n                                          background: '#25D366',\n                                          color: '#FFFFFF',\n                                          fontSize: '12px',\n                                          width: '32px',\n                                          height: '32px',\n                                          minWidth: '32px',\n                                          minHeight: '32px',\n                                          maxWidth: '32px',\n                                          maxHeight: '32px',\n                                          borderRadius: '50%'\n                                        }}\n                                      >\n                                        {champion.name.charAt(0).toUpperCase()}\n                                      </div>\n                                    )}\n                                  </div>\n                                  {/* Current User Indicator */}\n                                  {isCurrentUser && (\n                                    <div\n                                      className=\"absolute -top-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center animate-pulse\"\n                                      style={{\n                                        background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                        boxShadow: '0 2px 6px rgba(255,215,0,0.6)'\n                                      }}\n                                    >\n                                      <TbStar className=\"w-2.5 h-2.5 text-black\" />\n                                    </div>\n                                  )}\n                                </div>\n                              </div>\n\n                              {/* Center Section: User Info */}\n                              <div className=\"flex-1 min-w-0 px-2\">\n                                <div className=\"space-y-1\">\n                                  {/* User Name */}\n                                  <div className=\"flex items-center gap-2 mb-1\">\n                                    <h3\n                                      className=\"text-base md:text-lg font-bold truncate\"\n                                      style={{\n                                        color: champion.tier.nameColor,\n                                        textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                        filter: 'drop-shadow(0 0 4px currentColor)'\n                                      }}\n                                    >\n                                      {champion.name}\n                                    </h3>\n                                    {isCurrentUser && (\n                                      <span\n                                        className=\"px-2 py-0.5 rounded-full text-xs font-bold\"\n                                        style={{\n                                          background: 'linear-gradient(45deg, #FFD700, #FFA500)',\n                                          color: '#000000',\n                                          boxShadow: '0 2px 4px rgba(255,215,0,0.4)'\n                                        }}\n                                      >\n                                        YOU\n                                      </span>\n                                    )}\n                                  </div>\n\n                                  {/* League Badge */}\n                                  <div\n                                    className={`league-badge inline-flex items-center gap-1.5 px-3 py-1 bg-gradient-to-r ${champion.tier.color} rounded-lg text-xs font-bold`}\n                                    style={{\n                                      color: '#FFFFFF',\n                                      textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                                      border: `1px solid ${champion.tier.borderColor}`,\n                                      boxShadow: `0 2px 6px ${champion.tier.shadowColor}40`\n                                    }}\n                                  >\n                                    <champion.tier.icon className=\"w-3 h-3\" />\n                                    <span className=\"text-xs font-medium\">{champion.tier.title}</span>\n                                  </div>\n\n                                  {/* Class Info */}\n                                  <div className=\"text-xs text-white/70 mt-0.5\">\n                                    {champion.level} • Class {champion.class}\n                                  </div>\n                                </div>\n                              </div>\n\n                              {/* Right Section: Stats */}\n                              <div className=\"flex flex-col items-end gap-1 flex-shrink-0\">\n                                {/* XP Display */}\n                                <div\n                                  className=\"text-lg md:text-xl font-black mb-2\"\n                                  style={{\n                                    color: champion.tier.nameColor,\n                                    textShadow: `1px 1px 2px ${champion.tier.shadowColor}`,\n                                    filter: 'drop-shadow(0 0 6px currentColor)'\n                                  }}\n                                >\n                                  {champion.totalXP.toLocaleString()} XP\n                                </div>\n\n                                {/* Compact Stats */}\n                                <div className=\"flex items-center gap-3 text-xs\">\n                                  <div\n                                    className=\"flex items-center gap-1 px-2 py-1 rounded-md\"\n                                    style={{\n                                      backgroundColor: `${champion.tier.borderColor}20`,\n                                      color: champion.tier.textColor\n                                    }}\n                                  >\n                                    <TbBrain className=\"w-3 h-3\" />\n                                    <span className=\"font-medium\">{champion.totalQuizzesTaken}</span>\n                                  </div>\n                                  <div\n                                    className=\"flex items-center gap-1 px-2 py-1 rounded-md\"\n                                    style={{\n                                      backgroundColor: '#FF6B3520',\n                                      color: '#FF6B35'\n                                    }}\n                                  >\n                                    <TbFlame className=\"w-3 h-3\" />\n                                    <span className=\"font-medium\">{champion.currentStreak}</span>\n                                  </div>\n                                </div>\n\n                                {/* Subscription Badge */}\n                                {(() => {\n                                  const badge = getSubscriptionBadge(\n                                    champion.subscriptionStatus,\n                                    champion.subscriptionEndDate,\n                                    champion.subscriptionPlan,\n                                    champion.activePlanTitle,\n                                    actualRank\n                                  );\n                                  return (\n                                    <div\n                                      className=\"inline-flex items-center gap-1 px-2 py-0.5 rounded-md text-xs font-medium mt-2\"\n                                      style={{\n                                        backgroundColor: badge.bgColor,\n                                        color: badge.color,\n                                        border: `1px solid ${badge.borderColor}`,\n                                        fontSize: '10px'\n                                      }}\n                                    >\n                                      {badge.text}\n                                    </div>\n                                  );\n                                })()}\n                              </div>\n                            </div>\n                          </div>\n                        </motion.div>\n                      );\n                    })}\n                    </div>\n                  </div>\n                </motion.div>\n              )}\n\n              {/* DATA INTEGRATION STATUS */}\n              {rankingData.length > 0 && (\n                <motion.div\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1.8, duration: 0.8 }}\n                  className=\"mt-12 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-green-500/20 backdrop-blur-lg rounded-2xl p-6 border border-blue-400/30\"\n                >\n                  <div className=\"text-center\">\n                    <h3 className=\"text-xl font-bold mb-4\" style={{\n                      color: '#60A5FA',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      fontWeight: '800'\n                    }}>📊 Real User Data Integration</h3>\n                    <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm\">\n                      <div className=\"bg-green-500/20 rounded-lg p-3\">\n                        <div className=\"text-green-400 font-bold text-lg\">\n                          {rankingData.filter(u => u.dataSource === 'reports').length}\n                        </div>\n                        <div className=\"text-white/80\">📊 Live Quiz Data</div>\n                      </div>\n                      <div className=\"bg-blue-500/20 rounded-lg p-3\">\n                        <div className=\"text-blue-400 font-bold text-lg\">\n                          {rankingData.filter(u => u.dataSource === 'legacy_points').length}\n                        </div>\n                        <div className=\"text-white/80\">📈 Legacy Points</div>\n                      </div>\n                      <div className=\"bg-purple-500/20 rounded-lg p-3\">\n                        <div className=\"text-purple-400 font-bold text-lg\">\n                          {rankingData.filter(u => u.dataSource === 'estimated').length}\n                        </div>\n                        <div className=\"text-white/80\">🔮 Estimated Stats</div>\n                      </div>\n                    </div>\n                    <p className=\"text-white/70 text-sm mt-4\">\n                      Using real database users (admins excluded) with intelligent data processing\n                    </p>\n                  </div>\n                </motion.div>\n              )}\n\n              {/* CURRENT USER HIGHLIGHT */}\n              {currentUserRank && currentUserRank > 3 && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 1.5, duration: 0.8 }}\n                  className=\"mt-12 bg-gradient-to-r from-yellow-500/20 via-orange-500/20 to-red-500/20 backdrop-blur-lg rounded-2xl p-6 border border-yellow-400/30\"\n                >\n                  <div className=\"text-center\">\n                    <h3 className=\"text-2xl font-bold mb-2\" style={{\n                      color: '#ffffff',\n                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                      fontWeight: '800'\n                    }}>Your Current Position</h3>\n                    <div className=\"text-6xl font-black mb-2\" style={{\n                      color: '#fbbf24',\n                      textShadow: '3px 3px 6px rgba(0,0,0,0.8)',\n                      fontWeight: '900'\n                    }}>#{currentUserRank}</div>\n                    <p className=\"text-lg\" style={{\n                      color: '#e5e7eb',\n                      textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                      fontWeight: '600'\n                    }}>\n                      You're doing amazing! Keep pushing forward to reach the podium! 🚀\n                    </p>\n                  </div>\n                </motion.div>\n              )}\n\n              {/* MOTIVATIONAL FOOTER */}\n              <motion.div\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 2, duration: 0.8 }}\n                className=\"mt-16 text-center\"\n              >\n                <div className=\"bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-8 border border-white/10\">\n                  <motion.div\n                    animate={{ scale: [1, 1.05, 1] }}\n                    transition={{ duration: 3, repeat: Infinity }}\n                  >\n                    <TbRocket className=\"w-16 h-16 text-yellow-400 mx-auto mb-4\" />\n                  </motion.div>\n                  <h3 className=\"text-3xl font-bold mb-4\" style={{\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  }}>Ready to Rise Higher?</h3>\n                  <p className=\"text-xl mb-6 max-w-2xl mx-auto\" style={{\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  }}>\n                    Every quiz you take, every challenge you conquer, brings you closer to greatness.\n                    Your journey to the top starts with the next question!\n                  </p>\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300\"\n                    onClick={() => window.location.href = '/user/quiz'}\n                  >\n                    Take a Quiz Now! 🎯\n                  </motion.button>\n                </div>\n              </motion.div>\n\n              {/* EMPTY STATE */}\n              {rankingData.length === 0 && !loading && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  className=\"text-center py-20\"\n                >\n                  <TbTrophy className=\"w-24 h-24 text-white/30 mx-auto mb-6\" />\n                  <h3 className=\"text-2xl font-bold mb-4\" style={{\n                    color: '#ffffff',\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                    fontWeight: '800'\n                  }}>No Champions Yet</h3>\n                  <p className=\"text-lg\" style={{\n                    color: '#e5e7eb',\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                    fontWeight: '600'\n                  }}>\n                    Be the first to take a quiz and claim your spot in the Hall of Champions!\n                  </p>\n                </motion.div>\n              )}\n            </div>\n          </motion.div>\n        )}\n      </div>\n    </div>\n    </>\n  );\n};\n\nexport default AmazingRankingPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,MAAM;AAC9B,SACEC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,SAAS,EACTC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,OAAO,EACPC,KAAK,EACLC,OAAO,EACPC,YAAY,EACZC,OAAO,EACPC,QAAQ,QACH,gBAAgB;AACvB,SAASC,uBAAuB,EAAEC,gBAAgB,EAAEC,cAAc,QAAQ,2BAA2B;AACrG,SAASC,WAAW,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEtD,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAMC,SAAS,GAAG/B,WAAW,CAAEgC,KAAK,IAAKA,KAAK,CAACC,KAAK,IAAI,CAAC,CAAC,CAAC;EAC3D,MAAMC,IAAI,GAAGH,SAAS,CAACG,IAAI,IAAI,IAAI;EACnC,MAAMC,QAAQ,GAAGlC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2C,OAAO,EAAEC,UAAU,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6C,eAAe,EAAEC,kBAAkB,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC+C,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAC,QAAQ,CAAC;EAClD,MAAM,CAACiD,SAAS,EAAEC,YAAY,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACmD,cAAc,EAAEC,iBAAiB,CAAC,GAAGpD,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACqD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACuD,UAAU,EAAEC,aAAa,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACyD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAAC2D,WAAW,EAAEC,cAAc,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC6D,cAAc,EAAEC,iBAAiB,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC+D,cAAc,EAAEC,iBAAiB,CAAC,GAAGhE,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACiE,YAAY,EAAEC,eAAe,CAAC,GAAGlE,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAMmE,SAAS,GAAGjE,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMkE,cAAc,GAAGlE,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMmE,aAAa,GAAGnE,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMoE,WAAW,GAAGpE,MAAM,CAAC,IAAI,CAAC;;EAEhC;EACA,MAAMqE,kBAAkB,GAAG,CACzB,qDAAqD,EACrD,6DAA6D,EAC7D,8DAA8D,EAC9D,wDAAwD,EACxD,4DAA4D,EAC5D,2DAA2D,EAC3D,yDAAyD,EACzD,6FAA6F,EAC7F,oDAAoD,EACpD,yDAAyD,CAC1D;;EAED;EACA,MAAMC,YAAY,GAAG;IACnBC,MAAM,EAAE;MACNC,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,wDAAwD;MAC/DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAExE,OAAO;MACbyE,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,kBAAkB;MAC/BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,aAAa;MACrBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,CAAC;MAAE;MAChBC,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE;IACZ,CAAC;IACDC,SAAS,EAAE;MACThB,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,uEAAuE;MAChFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE9D,SAAS;MACf+D,KAAK,EAAE,WAAW;MAClBC,WAAW,EAAE,gBAAgB;MAC7BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,mBAAmB;MAC3BC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE;IACZ,CAAC;IACDE,OAAO,EAAE;MACPjB,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,qEAAqE;MAC9EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAExD,QAAQ;MACdyD,KAAK,EAAE,SAAS;MAChBC,WAAW,EAAE,cAAc;MAC3BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,eAAe;MACvBC,UAAU,EAAE,KAAK;MACjBC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACDG,QAAQ,EAAE;MACRlB,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,uDAAuD;MAC9DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,0BAA0B;MACvCC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAEzD,OAAO;MACb0D,KAAK,EAAE,UAAU;MACjBC,WAAW,EAAE,UAAU;MACvBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,gBAAgB;MACxBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACDI,IAAI,EAAE;MACJnB,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,wEAAwE;MACjFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAEzE,QAAQ;MACd0E,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE,SAAS;MACtBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,WAAW;MACnBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACDK,MAAM,EAAE;MACNpB,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,sDAAsD;MAC7DC,OAAO,EAAE,oEAAoE;MAC7EC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,0BAA0B;MACvCC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAEjE,OAAO;MACbkE,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,WAAW;MACxBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,gBAAgB;MACxBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,GAAG;MACjBC,QAAQ,EAAE;IACZ,CAAC;IACDM,MAAM,EAAE;MACNrB,GAAG,EAAE,GAAG;MACRC,KAAK,EAAE,4DAA4D;MACnEC,OAAO,EAAE,wEAAwE;MACjFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,yBAAyB;MACtCC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAEvE,MAAM;MACZwE,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,UAAU;MACvBC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,aAAa;MACrBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,GAAG;MACjBC,QAAQ,EAAE;IACZ,CAAC;IACDO,MAAM,EAAE;MACNtB,GAAG,EAAE,CAAC;MACNC,KAAK,EAAE,yDAAyD;MAChEC,OAAO,EAAE,uEAAuE;MAChFC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,WAAW,EAAE,wBAAwB;MACrCC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAE/D,QAAQ;MACdgE,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,cAAc;MAC3BC,WAAW,EAAE,SAAS;MACtBC,MAAM,EAAE,aAAa;MACrBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,GAAG;MAChBC,YAAY,EAAE,CAAC;MAAE;MACjBC,QAAQ,EAAE;IACZ;EACF,CAAC;;EAED;EACA,MAAMQ,aAAa,GAAIC,EAAE,IAAK;IAC5B,KAAK,MAAM,CAACC,MAAM,EAAEC,MAAM,CAAC,IAAIC,MAAM,CAACC,OAAO,CAAC9B,YAAY,CAAC,EAAE;MAC3D,IAAI0B,EAAE,IAAIE,MAAM,CAAC1B,GAAG,EAAE,OAAO;QAAEyB,MAAM;QAAE,GAAGC;MAAO,CAAC;IACpD;IACA,OAAO;MAAED,MAAM,EAAE,QAAQ;MAAE,GAAG3B,YAAY,CAACwB;IAAO,CAAC;EACrD,CAAC;;EAED;EACA,MAAMO,kBAAkB,GAAIjE,KAAK,IAAK;IACpC,MAAMkE,OAAO,GAAG,CAAC,CAAC;IAElBlE,KAAK,CAACmE,OAAO,CAAClE,IAAI,IAAI;MACpB,MAAMmE,UAAU,GAAGT,aAAa,CAAC1D,IAAI,CAACoE,OAAO,CAAC;MAC9C,IAAI,CAACH,OAAO,CAACE,UAAU,CAACP,MAAM,CAAC,EAAE;QAC/BK,OAAO,CAACE,UAAU,CAACP,MAAM,CAAC,GAAG;UAC3BC,MAAM,EAAEM,UAAU;UAClBpE,KAAK,EAAE;QACT,CAAC;MACH;MACAkE,OAAO,CAACE,UAAU,CAACP,MAAM,CAAC,CAAC7D,KAAK,CAACsE,IAAI,CAAC;QACpC,GAAGrE,IAAI;QACPsE,IAAI,EAAEH,UAAU,CAAC;MACnB,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACAL,MAAM,CAACS,IAAI,CAACN,OAAO,CAAC,CAACC,OAAO,CAACM,SAAS,IAAI;MACxCP,OAAO,CAACO,SAAS,CAAC,CAACzE,KAAK,CAAC0E,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACP,OAAO,GAAGM,CAAC,CAACN,OAAO,CAAC;IAChE,CAAC,CAAC;IAEF,OAAOH,OAAO;EAChB,CAAC;;EAED;EACA,MAAMW,wBAAwB,GAAGA,CAACC,QAAQ,EAAEC,WAAW,KAAK;IAC1D,IAAI,CAACA,WAAW,EAAE,OAAO,IAAI;IAE7B,MAAMX,UAAU,GAAGT,aAAa,CAACoB,WAAW,CAACV,OAAO,IAAI,CAAC,CAAC;IAC1D,MAAMhD,WAAW,GAAGyD,QAAQ,CAACE,MAAM,CAAC/E,IAAI,IAAI;MAC1C,MAAM4D,MAAM,GAAGF,aAAa,CAAC1D,IAAI,CAACoE,OAAO,CAAC;MAC1C,OAAOR,MAAM,CAACA,MAAM,KAAKO,UAAU,CAACP,MAAM;IAC5C,CAAC,CAAC,CAACa,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACP,OAAO,GAAGM,CAAC,CAACN,OAAO,CAAC;IAExC,OAAO;MACLR,MAAM,EAAEO,UAAU;MAClBpE,KAAK,EAAEqB,WAAW;MAClB4D,QAAQ,EAAE5D,WAAW,CAAC6D,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKL,WAAW,CAACK,GAAG,CAAC,GAAG,CAAC;MACnEC,aAAa,EAAEhE,WAAW,CAACiE;IAC7B,CAAC;EACH,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAId,SAAS,IAAK;IACxC,IAAIhD,cAAc,KAAKgD,SAAS,EAAE;MAChC/C,iBAAiB,CAAC,IAAI,CAAC;MACvBF,iBAAiB,CAAC,KAAK,CAAC;IAC1B,CAAC,MAAM;MAAA,IAAAgE,qBAAA;MACL9D,iBAAiB,CAAC+C,SAAS,CAAC;MAC5BjD,iBAAiB,CAAC,IAAI,CAAC;MACvBF,cAAc,CAAC,EAAAkE,qBAAA,GAAA7D,YAAY,CAAC8C,SAAS,CAAC,cAAAe,qBAAA,uBAAvBA,qBAAA,CAAyBxF,KAAK,KAAI,EAAE,CAAC;IACtD;EACF,CAAC;;EAED;EACA,MAAMyF,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,WAAW,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;IACxG,OAAOA,WAAW,CAACV,MAAM,CAACnB,MAAM,IAAIlC,YAAY,CAACkC,MAAM,CAAC,IAAIlC,YAAY,CAACkC,MAAM,CAAC,CAAC7D,KAAK,CAACsF,MAAM,GAAG,CAAC,CAAC;EACpG,CAAC;;EAED;EACA,MAAMK,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFrF,UAAU,CAAC,IAAI,CAAC;MAChBsF,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;;MAEtD;MACA,IAAI;QACFD,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC5C,MAAMC,qBAAqB,GAAG,MAAMzG,gBAAgB,CAAC;UACnD0G,KAAK,EAAE,IAAI;UACXC,WAAW,EAAE,CAAA/F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgG,KAAK,KAAI,KAAK;UACjCC,eAAe,EAAE;QACnB,CAAC,CAAC;QAEFN,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEC,qBAAqB,CAAC;QAEhE,IAAIA,qBAAqB,IAAIA,qBAAqB,CAACK,OAAO,IAAIL,qBAAqB,CAACM,IAAI,EAAE;UACxFR,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;UAEhD;UACA,MAAMQ,YAAY,GAAGP,qBAAqB,CAACM,IAAI,CAACpB,MAAM,CAACsB,QAAQ,IAC5DA,QAAQ,CAACjC,OAAO,IAAIiC,QAAQ,CAACjC,OAAO,GAAG,CAAC,IACxCiC,QAAQ,CAACC,iBAAiB,IAAID,QAAQ,CAACC,iBAAiB,GAAG,CAC9D,CAAC;UAED,MAAMC,eAAe,GAAGH,YAAY,CAACI,GAAG,CAAC,CAACH,QAAQ,EAAEI,KAAK,MAAM;YAC7DtB,GAAG,EAAEkB,QAAQ,CAAClB,GAAG;YACjBuB,IAAI,EAAEL,QAAQ,CAACK,IAAI,IAAI,oBAAoB;YAC3CC,KAAK,EAAEN,QAAQ,CAACM,KAAK,IAAI,EAAE;YAC3BC,KAAK,EAAEP,QAAQ,CAACO,KAAK,IAAI,EAAE;YAC3BZ,KAAK,EAAEK,QAAQ,CAACL,KAAK,IAAI,EAAE;YAC3Ba,cAAc,EAAER,QAAQ,CAACS,YAAY,IAAI,EAAE;YAC3C1C,OAAO,EAAEiC,QAAQ,CAACjC,OAAO,IAAI,CAAC;YAC9BkC,iBAAiB,EAAED,QAAQ,CAACC,iBAAiB,IAAI,CAAC;YAClDS,YAAY,EAAEV,QAAQ,CAACU,YAAY,IAAI,CAAC;YACxCC,aAAa,EAAEX,QAAQ,CAACW,aAAa,IAAI,CAAC;YAC1CC,UAAU,EAAEZ,QAAQ,CAACY,UAAU,IAAI,CAAC;YACpCC,kBAAkB,EAAEb,QAAQ,CAACa,kBAAkB,IAAI,MAAM;YACzDC,IAAI,EAAEV,KAAK,GAAG,CAAC;YACfnC,IAAI,EAAEZ,aAAa,CAAC2C,QAAQ,CAACjC,OAAO,IAAI,CAAC,CAAC;YAC1CgD,UAAU,EAAE,IAAI;YAChBC,YAAY,EAAEhB,QAAQ,CAACgB,YAAY,IAAI,CAAC;YACxC;YACAC,YAAY,EAAEjB,QAAQ,CAACiB,YAAY,IAAI,CAAC;YACxCC,aAAa,EAAElB,QAAQ,CAACkB,aAAa,IAAI,GAAG;YAC5CC,UAAU,EAAEnB,QAAQ,CAACmB,UAAU,IAAI,CAAC;YACpCC,QAAQ,EAAEpB,QAAQ,CAACoB,QAAQ,IAAI,CAAC;YAChCC,YAAY,EAAErB,QAAQ,CAACqB,YAAY,IAAI,EAAE;YACzCC,UAAU,EAAE;UACd,CAAC,CAAC,CAAC;UAEHxH,cAAc,CAACoG,eAAe,CAAC;;UAE/B;UACA,MAAMqB,aAAa,GAAGrB,eAAe,CAACtB,SAAS,CAAC4C,IAAI,IAAIA,IAAI,CAAC1C,GAAG,MAAKnF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmF,GAAG,EAAC;UAC/E5E,kBAAkB,CAACqH,aAAa,IAAI,CAAC,GAAGA,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC;;UAEjE;UACA,IAAI5H,IAAI,EAAE;YACR,MAAM8H,cAAc,GAAGlD,wBAAwB,CAAC2B,eAAe,EAAEvG,IAAI,CAAC;YACtEmB,oBAAoB,CAAC2G,cAAc,CAAC;YACpCzG,cAAc,CAAC,CAAAyG,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE/H,KAAK,KAAI,EAAE,CAAC;UAC7C;;UAEA;UACA,MAAMgI,OAAO,GAAG/D,kBAAkB,CAACuC,eAAe,CAAC;UACnD5E,eAAe,CAACoG,OAAO,CAAC;UAExB1H,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF,CAAC,CAAC,OAAO2H,OAAO,EAAE;QAChBrC,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEoC,OAAO,CAAC;MACpE;;MAEA;MACArC,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;MAE1D,IAAIqC,eAAe,EAAEC,aAAa;MAElC,IAAI;QACFvC,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;QACpDqC,eAAe,GAAG,MAAM9I,uBAAuB,CAAC,CAAC;QACjDwG,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;QACvCsC,aAAa,GAAG,MAAM5I,WAAW,CAAC,CAAC;MACrC,CAAC,CAAC,OAAO6I,KAAK,EAAE;QACdxC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEuC,KAAK,CAAC;QACnD,IAAI;UACFD,aAAa,GAAG,MAAM5I,WAAW,CAAC,CAAC;QACrC,CAAC,CAAC,OAAO8I,SAAS,EAAE;UAClBzC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEwC,SAAS,CAAC;QACpD;MACF;MAEA,IAAI7B,eAAe,GAAG,EAAE;MAExB,IAAI2B,aAAa,IAAIA,aAAa,CAAChC,OAAO,IAAIgC,aAAa,CAAC/B,IAAI,EAAE;QAChER,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;QAEhD;QACA,MAAMyC,cAAc,GAAG,CAAC,CAAC;QACzB,IAAIJ,eAAe,IAAIA,eAAe,CAAC/B,OAAO,IAAI+B,eAAe,CAAC9B,IAAI,EAAE;UACtE8B,eAAe,CAAC9B,IAAI,CAACjC,OAAO,CAAC2D,IAAI,IAAI;YAAA,IAAAS,UAAA;YACnC,MAAMC,MAAM,GAAG,EAAAD,UAAA,GAAAT,IAAI,CAAC7H,IAAI,cAAAsI,UAAA,uBAATA,UAAA,CAAWnD,GAAG,KAAI0C,IAAI,CAACU,MAAM;YAC5C,IAAIA,MAAM,EAAE;cACVF,cAAc,CAACE,MAAM,CAAC,GAAGV,IAAI,CAACW,OAAO,IAAI,EAAE;YAC7C;UACF,CAAC,CAAC;QACJ;QAEAjC,eAAe,GAAG2B,aAAa,CAAC/B,IAAI,CACjCpB,MAAM,CAACsB,QAAQ,IAAIA,QAAQ,IAAIA,QAAQ,CAAClB,GAAG,IAAIkB,QAAQ,CAACoC,IAAI,KAAK,OAAO,CAAC,CAAC;QAAA,CAC1EjC,GAAG,CAAC,CAACH,QAAQ,EAAEI,KAAK,KAAK;UACxB;UACA,MAAMiC,WAAW,GAAGL,cAAc,CAAChC,QAAQ,CAAClB,GAAG,CAAC,IAAI,EAAE;;UAEtD;UACA,IAAIwD,YAAY,GAAGD,WAAW,CAACrD,MAAM,IAAIgB,QAAQ,CAACC,iBAAiB,IAAI,CAAC;UACxE,IAAIsC,UAAU,GAAGF,WAAW,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAKD,GAAG,IAAIC,MAAM,CAACC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;UAClF,IAAIjC,YAAY,GAAG4B,YAAY,GAAG,CAAC,GAAGM,IAAI,CAACC,KAAK,CAACN,UAAU,GAAGD,YAAY,CAAC,GAAGtC,QAAQ,CAACU,YAAY,IAAI,CAAC;;UAExG;UACA,IAAI,CAAC2B,WAAW,CAACrD,MAAM,IAAIgB,QAAQ,CAAC8C,WAAW,EAAE;YAC/C;YACA,MAAMC,gBAAgB,GAAGH,IAAI,CAACI,GAAG,CAAC,CAAC,EAAEJ,IAAI,CAACK,KAAK,CAACjD,QAAQ,CAAC8C,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;YAC9E,MAAMI,gBAAgB,GAAGN,IAAI,CAAC9G,GAAG,CAAC,EAAE,EAAE8G,IAAI,CAACI,GAAG,CAAC,EAAE,EAAE,EAAE,GAAIhD,QAAQ,CAAC8C,WAAW,GAAGC,gBAAgB,GAAG,EAAG,CAAC,CAAC,CAAC,CAAC;;YAE1GT,YAAY,GAAGS,gBAAgB;YAC/BrC,YAAY,GAAGkC,IAAI,CAACC,KAAK,CAACK,gBAAgB,CAAC;YAC3CX,UAAU,GAAGK,IAAI,CAACC,KAAK,CAACnC,YAAY,GAAG4B,YAAY,CAAC;YAEpDhD,OAAO,CAACC,GAAG,CAAE,0BAAyBS,QAAQ,CAACK,IAAK,KAAI0C,gBAAiB,aAAYG,gBAAiB,cAAalD,QAAQ,CAAC8C,WAAY,SAAQ,CAAC;UACnJ;;UAEA;UACA,IAAI/E,OAAO,GAAGiC,QAAQ,CAACjC,OAAO,IAAI,CAAC;UAEnC,IAAI,CAACA,OAAO,EAAE;YACZ;YACA,IAAIiC,QAAQ,CAAC8C,WAAW,EAAE;cACxB;cACA/E,OAAO,GAAG6E,IAAI,CAACK,KAAK,CAClBjD,QAAQ,CAAC8C,WAAW;cAAG;cACtBR,YAAY,GAAG,EAAG;cAAG;cACrB5B,YAAY,GAAG,EAAE,GAAG4B,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC;cAAG;cAC7C5B,YAAY,GAAG,EAAE,GAAG4B,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;cAC9C,CAAC;YACH,CAAC,MAAM,IAAIA,YAAY,GAAG,CAAC,EAAE;cAC3B;cACAvE,OAAO,GAAG6E,IAAI,CAACK,KAAK,CACjBvC,YAAY,GAAG4B,YAAY,GAAG,CAAC;cAAI;cACnCA,YAAY,GAAG,EAAG;cAAG;cACrB5B,YAAY,GAAG,EAAE,GAAG4B,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;cAC9C,CAAC;YACH;UACF;;UAEA;UACA,IAAI3B,aAAa,GAAGX,QAAQ,CAACW,aAAa,IAAI,CAAC;UAC/C,IAAIC,UAAU,GAAGZ,QAAQ,CAACY,UAAU,IAAI,CAAC;UAEzC,IAAIyB,WAAW,CAACrD,MAAM,GAAG,CAAC,EAAE;YAC1B;YACA,IAAImE,UAAU,GAAG,CAAC;YAClBd,WAAW,CAACxE,OAAO,CAAC6E,MAAM,IAAI;cAC5B,IAAIA,MAAM,CAACC,KAAK,IAAI,EAAE,EAAE;gBAAE;gBACxBQ,UAAU,EAAE;gBACZvC,UAAU,GAAGgC,IAAI,CAACI,GAAG,CAACpC,UAAU,EAAEuC,UAAU,CAAC;cAC/C,CAAC,MAAM;gBACLA,UAAU,GAAG,CAAC;cAChB;YACF,CAAC,CAAC;YACFxC,aAAa,GAAGwC,UAAU;UAC5B,CAAC,MAAM,IAAInD,QAAQ,CAAC8C,WAAW,IAAI,CAACnC,aAAa,EAAE;YACjD;YACA,MAAMyC,aAAa,GAAGd,YAAY,GAAG,CAAC,GAAGtC,QAAQ,CAAC8C,WAAW,GAAGR,YAAY,GAAG,CAAC;YAChF,IAAIc,aAAa,GAAG,EAAE,EAAE;cACtBzC,aAAa,GAAGiC,IAAI,CAAC9G,GAAG,CAACwG,YAAY,EAAEM,IAAI,CAACK,KAAK,CAACG,aAAa,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;cACxExC,UAAU,GAAGgC,IAAI,CAACI,GAAG,CAACrC,aAAa,EAAEiC,IAAI,CAACK,KAAK,CAACG,aAAa,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACxE;UACF;;UAEA,OAAO;YACLtE,GAAG,EAAEkB,QAAQ,CAAClB,GAAG;YACjBuB,IAAI,EAAEL,QAAQ,CAACK,IAAI,IAAI,oBAAoB;YAC3CC,KAAK,EAAEN,QAAQ,CAACM,KAAK,IAAI,EAAE;YAC3BC,KAAK,EAAEP,QAAQ,CAACO,KAAK,IAAI,EAAE;YAC3BZ,KAAK,EAAEK,QAAQ,CAACL,KAAK,IAAI,EAAE;YAC3Ba,cAAc,EAAER,QAAQ,CAACQ,cAAc,IAAI,EAAE;YAC7CzC,OAAO,EAAEA,OAAO;YAChBkC,iBAAiB,EAAEqC,YAAY;YAC/B5B,YAAY,EAAEA,YAAY;YAC1BC,aAAa,EAAEA,aAAa;YAC5BC,UAAU,EAAEA,UAAU;YACtBC,kBAAkB,EAAEb,QAAQ,CAACa,kBAAkB,IAAI,MAAM;YACzDC,IAAI,EAAEV,KAAK,GAAG,CAAC;YACfnC,IAAI,EAAEZ,aAAa,CAACU,OAAO,CAAC;YAC5BgD,UAAU,EAAE,IAAI;YAChB;YACAsC,cAAc,EAAErD,QAAQ,CAAC8C,WAAW,IAAI,CAAC;YACzCQ,UAAU,EAAEjB,WAAW,CAACrD,MAAM,GAAG,CAAC;YAClCsC,UAAU,EAAEe,WAAW,CAACrD,MAAM,GAAG,CAAC,GAAG,SAAS,GAAGgB,QAAQ,CAAC8C,WAAW,GAAG,eAAe,GAAG;UAC5F,CAAC;QACH,CAAC,CAAC;;QAEJ;QACA5C,eAAe,CAAC9B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACP,OAAO,GAAGM,CAAC,CAACN,OAAO,CAAC;;QAErD;QACAmC,eAAe,CAACrC,OAAO,CAAC,CAAClE,IAAI,EAAEyG,KAAK,KAAK;UACvCzG,IAAI,CAACmH,IAAI,GAAGV,KAAK,GAAG,CAAC;QACvB,CAAC,CAAC;QAEFtG,cAAc,CAACoG,eAAe,CAAC;;QAE/B;QACA,IAAIvB,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAIhF,IAAI,EAAE;UACR;UACAgF,QAAQ,GAAGuB,eAAe,CAACtB,SAAS,CAAC4C,IAAI,IAAIA,IAAI,CAAC1C,GAAG,KAAKnF,IAAI,CAACmF,GAAG,CAAC;;UAEnE;UACA,IAAIH,QAAQ,KAAK,CAAC,CAAC,EAAE;YACnBA,QAAQ,GAAGuB,eAAe,CAACtB,SAAS,CAAC4C,IAAI,IAAI+B,MAAM,CAAC/B,IAAI,CAAC1C,GAAG,CAAC,KAAKyE,MAAM,CAAC5J,IAAI,CAACmF,GAAG,CAAC,CAAC;UACrF;;UAEA;UACA,IAAIH,QAAQ,KAAK,CAAC,CAAC,IAAIhF,IAAI,CAAC0G,IAAI,EAAE;YAChC1B,QAAQ,GAAGuB,eAAe,CAACtB,SAAS,CAAC4C,IAAI,IAAIA,IAAI,CAACnB,IAAI,KAAK1G,IAAI,CAAC0G,IAAI,CAAC;UACvE;QACF;QAEAnG,kBAAkB,CAACyE,QAAQ,IAAI,CAAC,GAAGA,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC;;QAEvD;QACA,IAAIhF,IAAI,EAAE;UACR,MAAM8H,cAAc,GAAGlD,wBAAwB,CAAC2B,eAAe,EAAEvG,IAAI,CAAC;UACtEmB,oBAAoB,CAAC2G,cAAc,CAAC;UACpCzG,cAAc,CAAC,CAAAyG,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE/H,KAAK,KAAI,EAAE,CAAC;QAC7C;;QAEA;QACA,MAAMgI,OAAO,GAAG/D,kBAAkB,CAACuC,eAAe,CAAC;QACnD5E,eAAe,CAACoG,OAAO,CAAC;;QAExB;QACApC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;UAC7Cd,WAAW,EAAE9E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0G,IAAI;UACvB6B,MAAM,EAAEvI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmF,GAAG;UACjB0E,UAAU,EAAE,QAAO7J,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmF,GAAG;UAC5B2E,OAAO,EAAE,CAAA9J,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyI,IAAI,MAAK,OAAO,KAAIzI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8J,OAAO;UAChDC,MAAM,EAAE/J,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoE,OAAO;UACrBwD,aAAa,EAAE5C,QAAQ;UACvBgF,gBAAgB,EAAEhF,QAAQ,IAAI,CAAC,GAAGA,QAAQ,GAAG,CAAC,GAAG,IAAI;UACrDiF,gBAAgB,EAAE1D,eAAe,CAAClB,MAAM;UACxC6E,eAAe,EAAE3D,eAAe,CAAC4D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC3D,GAAG,CAACtB,CAAC,KAAK;YAAEkF,EAAE,EAAElF,CAAC,CAACC,GAAG;YAAEkF,IAAI,EAAE,OAAOnF,CAAC,CAACC,GAAG;YAAEuB,IAAI,EAAExB,CAAC,CAACwB;UAAK,CAAC,CAAC,CAAC;UACxG4D,UAAU,EAAE/D,eAAe,CAACgE,IAAI,CAAC1C,IAAI,IAAIA,IAAI,CAAC1C,GAAG,MAAKnF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmF,GAAG,EAAC;UAChEqF,WAAW,EAAEjE,eAAe,CAACgE,IAAI,CAAC1C,IAAI,IAAI+B,MAAM,CAAC/B,IAAI,CAAC1C,GAAG,CAAC,KAAKyE,MAAM,CAAC5J,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmF,GAAG,CAAC,CAAC;UACjFsF,SAAS,EAAElE,eAAe,CAACgE,IAAI,CAAC1C,IAAI,IAAIA,IAAI,CAACnB,IAAI,MAAK1G,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0G,IAAI;QAClE,CAAC,CAAC;;QAEF;QACA,MAAMgE,WAAW,GAAG;UAClBlC,OAAO,EAAEjC,eAAe,CAACxB,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACyC,UAAU,KAAK,SAAS,CAAC,CAACtC,MAAM;UACvEsF,aAAa,EAAEpE,eAAe,CAACxB,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACyC,UAAU,KAAK,eAAe,CAAC,CAACtC,MAAM;UACnFuF,SAAS,EAAErE,eAAe,CAACxB,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACyC,UAAU,KAAK,WAAW,CAAC,CAACtC;QACvE,CAAC;QAEDM,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEW,eAAe,CAAClB,MAAM,EAAE,gBAAgB,CAAC;QACxFM,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE8E,WAAW,CAAC;QAC5C/E,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEW,eAAe,CAAC4D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC3D,GAAG,CAACtB,CAAC,KAAK;UACvEwB,IAAI,EAAExB,CAAC,CAACwB,IAAI;UACZ/C,EAAE,EAAEuB,CAAC,CAACd,OAAO;UACbyG,OAAO,EAAE3F,CAAC,CAACoB,iBAAiB;UAC5BwE,GAAG,EAAE5F,CAAC,CAAC6B,YAAY;UACnBgE,MAAM,EAAE7F,CAAC,CAACyC;QACZ,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,MAAM;QACLhC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxCzF,cAAc,CAAC,EAAE,CAAC;QAClBI,kBAAkB,CAAC,IAAI,CAAC;QACxBvC,OAAO,CAACgN,OAAO,CAAC,0DAA0D,CAAC;MAC7E;IACF,CAAC,CAAC,OAAO7C,KAAK,EAAE;MACdxC,OAAO,CAACwC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDnK,OAAO,CAACmK,KAAK,CAAC,8DAA8D,CAAC;IAC/E,CAAC,SAAS;MACR9H,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA3C,SAAS,CAAC,MAAM;IACdgI,gBAAgB,CAAC,CAAC;;IAElB;IACA,MAAMuF,WAAW,GAAGjJ,kBAAkB,CAACiH,IAAI,CAACK,KAAK,CAACL,IAAI,CAACiC,MAAM,CAAC,CAAC,GAAGlJ,kBAAkB,CAACqD,MAAM,CAAC,CAAC;IAC7FtE,oBAAoB,CAACkK,WAAW,CAAC;;IAEjC;IACA,MAAME,cAAc,GAAGC,WAAW,CAAC,MAAM;MACvCvK,iBAAiB,CAACwK,IAAI,IAAI,CAACA,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC,EAAE,IAAI,CAAC;;IAER;IACA;IACA;IACA;IACA;;IAEA;IACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;MAC9B3F,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAC7DF,gBAAgB,CAAC,CAAC;IACpB,CAAC;;IAED;IACA,MAAM6F,mBAAmB,GAAIC,KAAK,IAAK;MACrC7F,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE4F,KAAK,CAACC,MAAM,CAAC;MACnE;MACAC,UAAU,CAAC,MAAM;QACfhG,gBAAgB,CAAC,CAAC;MACpB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACZ,CAAC;;IAEDiG,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAEN,iBAAiB,CAAC;IACnDK,MAAM,CAACC,gBAAgB,CAAC,eAAe,EAAEL,mBAAmB,CAAC;IAE7D,OAAO,MAAM;MACXM,aAAa,CAACV,cAAc,CAAC;MAC7B;MACAQ,MAAM,CAACG,mBAAmB,CAAC,OAAO,EAAER,iBAAiB,CAAC;MACtDK,MAAM,CAACG,mBAAmB,CAAC,eAAe,EAAEP,mBAAmB,CAAC;IAClE,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMQ,aAAa,GAAG7L,WAAW,CAACiK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAC7C,MAAM6B,eAAe,GAAG9L,WAAW,CAACiK,KAAK,CAAC,CAAC,CAAC;;EAE5C;EACA,MAAM8B,YAAY,GAAGA,CAAA,KAAM;IACzBtG,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;;IAEzC;IACA,MAAMZ,QAAQ,GAAG,EAAE;;IAEnB;IACAhH,OAAO,CAACkI,OAAO,CAAE,uCAAsClB,QAAS,MAAK,CAAC;;IAEtE;IACA,MAAMkH,WAAW,GAAGC,QAAQ,CAACC,aAAa,CAAC,uBAAuB,CAAC;IACnE,IAAIF,WAAW,EAAE;MACfvG,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;;MAE1D;MACAsG,WAAW,CAACG,cAAc,CAAC;QACzBC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE;MACT,CAAC,CAAC;;MAEF;MACAb,UAAU,CAAC,MAAM;QACf;QACA,MAAMc,iBAAiB,GAAG,CACxB,kBAAkB,EAClB,kBAAkB,EAClB,kBAAkB,EAClB,kBAAkB,EAClB,mBAAmB,CACpB;QAED,IAAIC,KAAK,GAAG,KAAK;QACjB,KAAK,IAAIC,QAAQ,IAAIF,iBAAiB,EAAE;UACtC,MAAMG,KAAK,GAAGT,WAAW,CAACU,gBAAgB,CAACF,QAAQ,CAAC;UACpD/G,OAAO,CAACC,GAAG,CAAE,SAAQ+G,KAAK,CAACtH,MAAO,yBAAwBqH,QAAS,EAAC,CAAC;UAErE,IAAIC,KAAK,CAACtH,MAAM,IAAI,EAAE,EAAE;YAAE;YACxB,MAAMwH,WAAW,GAAG5D,IAAI,CAAC9G,GAAG,CAAC,EAAE,EAAEwK,KAAK,CAACtH,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;YACpD,MAAMyH,UAAU,GAAGH,KAAK,CAACE,WAAW,CAAC;YAErC,IAAIC,UAAU,EAAE;cACdnH,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;cAEjEkH,UAAU,CAACT,cAAc,CAAC;gBACxBC,QAAQ,EAAE,QAAQ;gBAClBC,KAAK,EAAE;cACT,CAAC,CAAC;;cAEF;cACA,MAAMQ,aAAa,GAAG;gBACpBC,MAAM,EAAEF,UAAU,CAACG,KAAK,CAACD,MAAM;gBAC/BE,SAAS,EAAEJ,UAAU,CAACG,KAAK,CAACC,SAAS;gBACrCC,SAAS,EAAEL,UAAU,CAACG,KAAK,CAACE,SAAS;gBACrCC,eAAe,EAAEN,UAAU,CAACG,KAAK,CAACG,eAAe;gBACjDC,OAAO,EAAEP,UAAU,CAACG,KAAK,CAACI;cAC5B,CAAC;;cAED;cACAP,UAAU,CAACG,KAAK,CAACD,MAAM,GAAG,8BAA8B;cACxDF,UAAU,CAACG,KAAK,CAACC,SAAS,GAAG,sEAAsE;cACnGJ,UAAU,CAACG,KAAK,CAACE,SAAS,GAAG,aAAa;cAC1CL,UAAU,CAACG,KAAK,CAACG,eAAe,GAAG,wBAAwB;cAC3DN,UAAU,CAACG,KAAK,CAACI,OAAO,GAAG,mBAAmB;cAC9CP,UAAU,CAACG,KAAK,CAACK,UAAU,GAAG,eAAe;cAC7CR,UAAU,CAACG,KAAK,CAACM,MAAM,GAAG,MAAM;;cAEhC;cACA,IAAIC,UAAU,GAAG,CAAC;cAClB,MAAMC,aAAa,GAAGrC,WAAW,CAAC,MAAM;gBACtC,IAAIoC,UAAU,GAAG,CAAC,EAAE;kBAAE;kBACpBV,UAAU,CAACG,KAAK,CAACE,SAAS,GAAGK,UAAU,GAAG,CAAC,KAAK,CAAC,GAAG,aAAa,GAAG,aAAa;kBACjFV,UAAU,CAACG,KAAK,CAACC,SAAS,GAAGM,UAAU,GAAG,CAAC,KAAK,CAAC,GAC/C,sEAAsE,GACtE,sEAAsE;kBACxEA,UAAU,EAAE;gBACd,CAAC,MAAM;kBACL3B,aAAa,CAAC4B,aAAa,CAAC;gBAC9B;cACF,CAAC,EAAE,GAAG,CAAC;;cAEP;cACA/B,UAAU,CAAC,MAAM;gBACfoB,UAAU,CAACG,KAAK,CAACD,MAAM,GAAGD,aAAa,CAACC,MAAM;gBAC9CF,UAAU,CAACG,KAAK,CAACC,SAAS,GAAGH,aAAa,CAACG,SAAS;gBACpDJ,UAAU,CAACG,KAAK,CAACE,SAAS,GAAGJ,aAAa,CAACI,SAAS;gBACpDL,UAAU,CAACG,KAAK,CAACG,eAAe,GAAGL,aAAa,CAACK,eAAe;gBAChEN,UAAU,CAACG,KAAK,CAACI,OAAO,GAAGN,aAAa,CAACM,OAAO;gBAChDP,UAAU,CAACG,KAAK,CAACM,MAAM,GAAG,EAAE;cAC9B,CAAC,EAAE,IAAI,CAAC;cAERd,KAAK,GAAG,IAAI;cACZ;YACF;UACF;QACF;QAEA,IAAI,CAACA,KAAK,EAAE;UACV9G,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;UACvE5H,OAAO,CAAC0P,IAAI,CAAC,2EAA2E,CAAC;QAC3F;MACF,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,MAAM;MACL/H,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACpD;MACA,MAAM+H,iBAAiB,GAAGhC,MAAM,CAACiC,WAAW,GAAG,CAAC,CAAC,CAAC;MAClDjC,MAAM,CAACkC,QAAQ,CAAC;QACdC,GAAG,EAAEH,iBAAiB;QACtBrB,QAAQ,EAAE;MACZ,CAAC,CAAC;MACFtO,OAAO,CAAC0P,IAAI,CAAC,mDAAmD,CAAC;IACnE;EACF,CAAC;;EAED;EACA,MAAMK,oBAAoB,GAAGA,CAAC7G,kBAAkB,EAAE8G,mBAAmB,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,SAAS,GAAG,CAAC,KAAK;IAC1H,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,OAAO,GAAGN,mBAAmB,GAAG,IAAIK,IAAI,CAACL,mBAAmB,CAAC,GAAG,IAAI;IAE1ErI,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;MACjCsB,kBAAkB;MAClB8G,mBAAmB;MACnBC,gBAAgB;MAChBC,eAAe;MACfI,OAAO;MACPF,GAAG;MACHG,QAAQ,EAAED,OAAO,IAAIA,OAAO,GAAGF,GAAG;MAClCD;IACF,CAAC,CAAC;;IAEF;IACA,IAAIjH,kBAAkB,KAAK,QAAQ,IAAIA,kBAAkB,KAAK,SAAS,EAAE;MACvE;MACA,IAAI,CAACoH,OAAO,IAAIA,OAAO,GAAGF,GAAG,EAAE;QAC7B;QACA,OAAO;UACLI,IAAI,EAAE,WAAW;UACjBpM,KAAK,EAAE,SAAS;UAAE;UAClBC,OAAO,EAAE,yBAAyB;UAClCQ,WAAW,EAAE;QACf,CAAC;MACH,CAAC,MAAM;QACL;QACA,OAAO;UACL2L,IAAI,EAAE,SAAS;UACfpM,KAAK,EAAE,SAAS;UAAE;UAClBC,OAAO,EAAE,wBAAwB;UACjCQ,WAAW,EAAE;QACf,CAAC;MACH;IACF,CAAC,MAAM;MACL;MACA,OAAO;QACL2L,IAAI,EAAE,SAAS;QACfpM,KAAK,EAAE,SAAS;QAAE;QAClBC,OAAO,EAAE,wBAAwB;QACjCQ,WAAW,EAAE;MACf,CAAC;IACH;EACF,CAAC;;EAED;EACA,IAAIzC,OAAO,IAAIF,WAAW,CAACmF,MAAM,KAAK,CAAC,EAAE;IACvC,oBACE7F,OAAA;MAAKiP,SAAS,EAAC,4GAA4G;MAAAC,QAAA,eACzHlP,OAAA,CAAC5B,MAAM,CAAC+Q,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBC,OAAO,EAAE;UAAED,OAAO,EAAE;QAAE,CAAE;QACxBJ,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAEvBlP,OAAA,CAAC5B,MAAM,CAAC+Q,GAAG;UACTG,OAAO,EAAE;YAAEC,MAAM,EAAE;UAAI,CAAE;UACzBzB,UAAU,EAAE;YAAE0B,QAAQ,EAAE,CAAC;YAAEC,MAAM,EAAEC,QAAQ;YAAEC,IAAI,EAAE;UAAS,CAAE;UAC9DV,SAAS,EAAC;QAAqF;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChG,CAAC,eACF/P,OAAA;UAAGiP,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAAgC;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,oBACE/P,OAAA,CAAAE,SAAA;IAAAgP,QAAA,gBACElP,OAAA;MAAOgQ,GAAG;MAAAd,QAAA,EAAG;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eACV/P,OAAA;MAAKiP,SAAS,EAAC,+GAA+G;MAAAC,QAAA,gBAE9HlP,OAAA;QAAKiP,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/ClP,OAAA;UAAKiP,SAAS,EAAC;QAAyH;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/I/P,OAAA;UAAKiP,SAAS,EAAC;QAAgJ;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtK/P,OAAA;UAAKiP,SAAS,EAAC;QAA6I;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnK/P,OAAA;UAAKiP,SAAS,EAAC;QAA8I;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjK,CAAC,eAGN/P,OAAA;QAAKiP,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClE,CAAC,GAAGe,KAAK,CAAC,EAAE,CAAC,CAAC,CAACjJ,GAAG,CAAC,CAACkJ,CAAC,EAAEC,CAAC,kBACvBnQ,OAAA,CAAC5B,MAAM,CAAC+Q,GAAG;UAETF,SAAS,EAAC,mDAAmD;UAC7DK,OAAO,EAAE;YACPc,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;YACfC,CAAC,EAAE,CAAC,CAAC,EAAE5G,IAAI,CAACiC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC,CAAC;YACnC2D,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;UACzB,CAAE;UACFvB,UAAU,EAAE;YACV0B,QAAQ,EAAE,CAAC,GAAG/F,IAAI,CAACiC,MAAM,CAAC,CAAC,GAAG,CAAC;YAC/B+D,MAAM,EAAEC,QAAQ;YAChBY,KAAK,EAAE7G,IAAI,CAACiC,MAAM,CAAC,CAAC,GAAG;UACzB,CAAE;UACF+B,KAAK,EAAE;YACL8C,IAAI,EAAG,GAAE9G,IAAI,CAACiC,MAAM,CAAC,CAAC,GAAG,GAAI,GAAE;YAC/B4C,GAAG,EAAG,GAAE7E,IAAI,CAACiC,MAAM,CAAC,CAAC,GAAG,GAAI;UAC9B;QAAE,GAfGyE,CAAC;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgBP,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN/P,OAAA;QAAKiP,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAE5BlP,OAAA,CAAC5B,MAAM,CAAC+Q,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEe,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCd,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEe,CAAC,EAAE;UAAE,CAAE;UAC9BtC,UAAU,EAAE;YAAE0B,QAAQ,EAAE;UAAI,CAAE;UAC9BP,SAAS,EAAC,4DAA4D;UAAAC,QAAA,eAEtElP,OAAA;YAAKiP,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChClP,OAAA;cAAKiP,SAAS,EAAC,sHAAsH;cAAAC,QAAA,eACnIlP,OAAA;gBAAKiP,SAAS,EAAC,wFAAwF;gBAAAC,QAAA,gBAGrGlP,OAAA,CAAC5B,MAAM,CAACoS,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAEA,CAAA,KAAMnQ,QAAQ,CAAC,WAAW,CAAE;kBACrCwO,SAAS,EAAC,gNAAgN;kBAC1NxB,KAAK,EAAE;oBACLoD,QAAQ,EAAE1E,MAAM,CAAC2E,UAAU,GAAG,GAAG,GAAG,MAAM,GAAG;kBAC/C,CAAE;kBAAA5B,QAAA,gBAEFlP,OAAA,CAACjB,MAAM;oBAACkQ,SAAS,EAAC;kBAAuB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5C/P,OAAA;oBAAAkP,QAAA,EAAM;kBAAG;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGhB/P,OAAA,CAAC5B,MAAM,CAACoS,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAEnE,YAAa;kBACtBwC,SAAS,EAAC,kNAAkN;kBAC5NxB,KAAK,EAAE;oBACLsD,UAAU,EAAE,0CAA0C;oBACtDnO,KAAK,EAAE,SAAS;oBAChBoO,UAAU,EAAE,MAAM;oBAClBC,UAAU,EAAE,KAAK;oBACjBJ,QAAQ,EAAE1E,MAAM,CAAC2E,UAAU,GAAG,GAAG,GAAG,MAAM,GAAG;kBAC/C,CAAE;kBAAA5B,QAAA,gBAEFlP,OAAA,CAACnB,QAAQ;oBAACoQ,SAAS,EAAC;kBAAuB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9C/P,OAAA;oBAAAkP,QAAA,EACGpO,eAAe,GAAI,YAAWA,eAAgB,EAAC,GAC9C,CAAAN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyI,IAAI,MAAK,OAAO,IAAIzI,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8J,OAAO,GAAI,YAAY,GAAG;kBAAS;oBAAAsF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC,eAGhB/P,OAAA,CAAC5B,MAAM,CAACoS,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAEA,CAAA,KAAM;oBACbzK,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;oBACtCD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE5F,IAAI,CAAC;oBACzC2F,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE5F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmF,GAAG,CAAC;oBAClCQ,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE5F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0G,IAAI,CAAC;oBACrCf,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE5F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8J,OAAO,CAAC;oBAC3CnE,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE5F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyI,IAAI,CAAC;oBACrC9C,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE1F,WAAW,CAACmF,MAAM,CAAC;oBACvDM,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE1F,WAAW,CAACiK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC3D,GAAG,CAACtB,CAAC,KAAK;sBACzEkF,EAAE,EAAElF,CAAC,CAACC,GAAG;sBACTuB,IAAI,EAAExB,CAAC,CAACwB,IAAI;sBACZS,IAAI,EAAEjC,CAAC,CAACiC,IAAI;sBACZ/C,OAAO,EAAEc,CAAC,CAACd;oBACb,CAAC,CAAC,CAAC,CAAC;oBACJuB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE1F,WAAW,CAACsG,GAAG,CAACtB,CAAC,IAAIA,CAAC,CAACC,GAAG,CAAC,CAAC;oBACpEQ,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE5F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmF,GAAG,CAAC;oBAC9CQ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE1F,WAAW,CAACqK,IAAI,CAACrF,CAAC,IAAIA,CAAC,CAACC,GAAG,MAAKnF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmF,GAAG,EAAC,CAAC;oBACjFQ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE9D,aAAa,CAAC4O,OAAO,CAAC;oBAC5D/K,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE7D,WAAW,CAAC2O,OAAO,CAAC;oBACxD/K,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEtF,eAAe,CAAC;;oBAEhD;oBACA,MAAMqQ,YAAY,GAAGxE,QAAQ,CAACS,gBAAgB,CAAE,kBAAiB5M,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmF,GAAI,IAAG,CAAC;oBAC/EQ,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE+K,YAAY,CAACtL,MAAM,CAAC;oBACxDsL,YAAY,CAACzM,OAAO,CAAC,CAAC0M,EAAE,EAAEjB,CAAC,KAAK;sBAC9BhK,OAAO,CAACC,GAAG,CAAE,WAAU+J,CAAE,GAAE,EAAEiB,EAAE,EAAE,OAAO,EAAEA,EAAE,CAACC,YAAY,CAAC,gBAAgB,CAAC,CAAC;oBAC9E,CAAC,CAAC;kBACJ,CAAE;kBACFpC,SAAS,EAAC,uDAAuD;kBAAAC,QAAA,EAClE;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC,eAGhB/P,OAAA,CAAC5B,MAAM,CAACoS,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAEA,CAAA,KAAM7O,iBAAiB,CAAC,CAACD,cAAc,CAAE;kBAClDmN,SAAS,EAAG,0JACVnN,cAAc,GACV,2DAA2D,GAC3D,2DACL,EAAE;kBACH2L,KAAK,EAAE;oBACLoD,QAAQ,EAAE1E,MAAM,CAAC2E,UAAU,GAAG,GAAG,GAAG,MAAM,GAAG;kBAC/C,CAAE;kBAAA5B,QAAA,gBAEFlP,OAAA,CAACT,OAAO;oBAAC0P,SAAS,EAAC;kBAAuB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC7C/P,OAAA;oBAAAkP,QAAA,EAAOpN,cAAc,GAAG,aAAa,GAAG;kBAAW;oBAAA8N,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,eAGhB/P,OAAA,CAAC5B,MAAM,CAACoS,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE,IAAI;oBAAEnB,MAAM,EAAE;kBAAI,CAAE;kBACzCoB,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAE1K,gBAAiB;kBAC1BoL,QAAQ,EAAE1Q,OAAQ;kBAClBqO,SAAS,EAAC,qNAAqN;kBAC/NxB,KAAK,EAAE;oBACLoD,QAAQ,EAAE1E,MAAM,CAAC2E,UAAU,GAAG,GAAG,GAAG,MAAM,GAAG;kBAC/C,CAAE;kBAAA5B,QAAA,gBAEFlP,OAAA,CAAChB,SAAS;oBAACiQ,SAAS,EAAG,yBAAwBrO,OAAO,GAAG,cAAc,GAAG,EAAG;kBAAE;oBAAAgP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClF/P,OAAA;oBAAAkP,QAAA,EAAM;kBAAO;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,EAGZ,CAAC,CAAAvP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyI,IAAI,MAAK,OAAO,KAAIzI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8J,OAAO,mBACvCtK,OAAA,CAAC5B,MAAM,CAAC+Q,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEe,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCd,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEe,CAAC,EAAE;UAAE,CAAE;UAC9BtC,UAAU,EAAE;YAAE0B,QAAQ,EAAE;UAAI,CAAE;UAC9BP,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAE7ClP,OAAA;YAAKiP,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChClP,OAAA;cAAKiP,SAAS,EAAC,gHAAgH;cAAAC,QAAA,eAC7HlP,OAAA;gBAAKiP,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtClP,OAAA;kBAAKiP,SAAS,EAAC,qEAAqE;kBAAAC,QAAA,eAClFlP,OAAA;oBAAMiP,SAAS,EAAC,8BAA8B;oBAAAC,QAAA,EAAC;kBAAE;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACN/P,OAAA;kBAAAkP,QAAA,gBACElP,OAAA;oBAAIiP,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,EAAC;kBAAU;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpD/P,OAAA;oBAAGiP,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAErC;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb,eAGD/P,OAAA,CAAC5B,MAAM,CAAC+Q,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEe,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCd,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEe,CAAC,EAAE;UAAE,CAAE;UAC9BtC,UAAU,EAAE;YAAE0B,QAAQ,EAAE,CAAC;YAAEG,IAAI,EAAE;UAAU,CAAE;UAC7CV,SAAS,EAAC,+BAA+B;UAAAC,QAAA,eAGzClP,OAAA;YAAKiP,SAAS,EAAC,iGAAiG;YAAAC,QAAA,gBAC9GlP,OAAA;cAAKiP,SAAS,EAAC;YAA6E;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnG/P,OAAA;cAAKiP,SAAS,EAAC;YAA+E;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAGrG/P,OAAA;cAAKiP,SAAS,EAAC,2EAA2E;cAAAC,QAAA,eACxFlP,OAAA;gBAAKiP,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,gBAG5ClP,OAAA,CAAC5B,MAAM,CAAC+Q,GAAG;kBACTG,OAAO,EAAE;oBACPoB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;oBACnBa,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;kBACnB,CAAE;kBACFzD,UAAU,EAAE;oBACV0B,QAAQ,EAAE,CAAC;oBACXC,MAAM,EAAEC,QAAQ;oBAChBC,IAAI,EAAE;kBACR,CAAE;kBACFV,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAExBlP,OAAA;oBAAIiP,SAAS,EAAC,iGAAiG;oBAAAC,QAAA,gBAC7GlP,OAAA,CAAC5B,MAAM,CAACoT,IAAI;sBACVlC,OAAO,EAAE;wBACPmC,kBAAkB,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ;sBACrD,CAAE;sBACF3D,UAAU,EAAE;wBACV0B,QAAQ,EAAE,CAAC;wBACXC,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR,CAAE;sBACFV,SAAS,EAAC,+HAA+H;sBACzIxB,KAAK,EAAE;wBACLiE,cAAc,EAAE,WAAW;wBAC3BC,oBAAoB,EAAE,MAAM;wBAC5BC,mBAAmB,EAAE,aAAa;wBAClCrM,MAAM,EAAE;sBACV,CAAE;sBAAA2J,QAAA,EACH;oBAED;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eACd/P,OAAA;sBAAA4P,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN/P,OAAA,CAAC5B,MAAM,CAACoT,IAAI;sBACVlC,OAAO,EAAE;wBACP0B,UAAU,EAAE,CACV,4DAA4D,EAC5D,0DAA0D,EAC1D,4DAA4D;sBAEhE,CAAE;sBACFlD,UAAU,EAAE;wBACV0B,QAAQ,EAAE,GAAG;wBACbC,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR,CAAE;sBACFlC,KAAK,EAAE;wBACL7K,KAAK,EAAE,SAAS;wBAChBqO,UAAU,EAAE,KAAK;wBACjBD,UAAU,EAAE;sBACd,CAAE;sBAAA9B,QAAA,EACH;oBAED;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eAGb/P,OAAA,CAAC5B,MAAM,CAACyT,CAAC;kBACPzC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEe,CAAC,EAAE;kBAAG,CAAE;kBAC/Bd,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEe,CAAC,EAAE;kBAAE,CAAE;kBAC9BtC,UAAU,EAAE;oBAAEwC,KAAK,EAAE,GAAG;oBAAEd,QAAQ,EAAE;kBAAI,CAAE;kBAC1CP,SAAS,EAAC,8GAA8G;kBACxHxB,KAAK,EAAE;oBACL7K,KAAK,EAAE,SAAS;oBAChBoO,UAAU,EAAE,6BAA6B;oBACzCD,UAAU,EAAE,0CAA0C;oBACtDY,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE;kBACvB,CAAE;kBAAA1C,QAAA,EACH;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAGX/P,OAAA,CAAC5B,MAAM,CAAC+Q,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEqB,KAAK,EAAE;kBAAI,CAAE;kBACpCpB,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEqB,KAAK,EAAE;kBAAE,CAAE;kBAClC5C,UAAU,EAAE;oBAAEwC,KAAK,EAAE,GAAG;oBAAEd,QAAQ,EAAE;kBAAI,CAAE;kBAC1CP,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAExBlP,OAAA;oBAAGiP,SAAS,EAAC,6JAA6J;oBACvKxB,KAAK,EAAE;sBACLuD,UAAU,EAAE,6BAA6B;sBACzCc,SAAS,EAAE;oBACb,CAAE;oBAAA5C,QAAA,EACF5N;kBAAiB;oBAAAsO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC,eAGb/P,OAAA,CAAC5B,MAAM,CAAC+Q,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEe,CAAC,EAAE;kBAAG,CAAE;kBAC/Bd,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEe,CAAC,EAAE;kBAAE,CAAE;kBAC9BtC,UAAU,EAAE;oBAAEwC,KAAK,EAAE,CAAC;oBAAEd,QAAQ,EAAE;kBAAI,CAAE;kBACxCP,SAAS,EAAC,2EAA2E;kBAAAC,QAAA,EAEpF,CACC;oBACEhM,IAAI,EAAE3D,OAAO;oBACbwS,KAAK,EAAErR,WAAW,CAACmF,MAAM;oBACzBmM,KAAK,EAAE,WAAW;oBAClBC,UAAU,EAAE,qDAAqD;oBACjEC,SAAS,EAAE,SAAS;oBACpB7O,WAAW,EAAE;kBACf,CAAC,EACD;oBACEH,IAAI,EAAEzE,QAAQ;oBACdsT,KAAK,EAAExF,aAAa,CAAC1G,MAAM;oBAC3BmM,KAAK,EAAE,gBAAgB;oBACvBC,UAAU,EAAE,oDAAoD;oBAChEC,SAAS,EAAE,SAAS;oBACpB7O,WAAW,EAAE;kBACf,CAAC,EACD;oBACEH,IAAI,EAAEtE,OAAO;oBACbmT,KAAK,EAAErR,WAAW,CAAC6E,MAAM,CAACG,CAAC,IAAIA,CAAC,CAAC8B,aAAa,GAAG,CAAC,CAAC,CAAC3B,MAAM;oBAC1DmM,KAAK,EAAE,gBAAgB;oBACvBC,UAAU,EAAE,gDAAgD;oBAC5DC,SAAS,EAAE,SAAS;oBACpB7O,WAAW,EAAE;kBACf,CAAC,EACD;oBACEH,IAAI,EAAEvE,MAAM;oBACZoT,KAAK,EAAErR,WAAW,CAAC2I,MAAM,CAAC,CAACC,GAAG,EAAE5D,CAAC,KAAK4D,GAAG,IAAI5D,CAAC,CAACd,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAACuN,cAAc,CAAC,CAAC;oBACjFH,KAAK,EAAE,UAAU;oBACjBC,UAAU,EAAE,qDAAqD;oBACjEC,SAAS,EAAE,SAAS;oBACpB7O,WAAW,EAAE;kBACf,CAAC,CACF,CAAC2D,GAAG,CAAC,CAACoL,IAAI,EAAEnL,KAAK,kBAChBjH,OAAA,CAAC5B,MAAM,CAAC+Q,GAAG;oBAETC,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEqB,KAAK,EAAE;oBAAI,CAAE;oBACpCpB,OAAO,EAAE;sBAAED,OAAO,EAAE,CAAC;sBAAEqB,KAAK,EAAE;oBAAE,CAAE;oBAClC5C,UAAU,EAAE;sBAAEwC,KAAK,EAAE,GAAG,GAAGrJ,KAAK,GAAG,GAAG;sBAAEuI,QAAQ,EAAE;oBAAI,CAAE;oBACxDiB,UAAU,EAAE;sBAAEC,KAAK,EAAE,IAAI;sBAAEN,CAAC,EAAE,CAAC;oBAAE,CAAE;oBACnCnB,SAAS,EAAG,qBAAoBmD,IAAI,CAACH,UAAW,8EAA8E;oBAC9HxE,KAAK,EAAE;sBACLD,MAAM,EAAG,aAAY4E,IAAI,CAAC/O,WAAY,IAAG;sBACzCqK,SAAS,EAAG,cAAa0E,IAAI,CAAC/O,WAAY;oBAC5C,CAAE;oBAAA6L,QAAA,gBAEFlP,OAAA;sBAAKiP,SAAS,EAAC;oBAAgE;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACtF/P,OAAA,CAACoS,IAAI,CAAClP,IAAI;sBACR+L,SAAS,EAAC,kDAAkD;sBAC5DxB,KAAK,EAAE;wBAAE7K,KAAK,EAAEwP,IAAI,CAACF,SAAS;wBAAE3M,MAAM,EAAE;sBAAyC;oBAAE;sBAAAqK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpF,CAAC,eACF/P,OAAA;sBACEiP,SAAS,EAAC,0EAA0E;sBACpFxB,KAAK,EAAE;wBACL7K,KAAK,EAAEwP,IAAI,CAACF,SAAS;wBACrBlB,UAAU,EAAG,6BAA4B;wBACzCzL,MAAM,EAAE,oCAAoC;wBAC5CsL,QAAQ,EAAE;sBACZ,CAAE;sBAAA3B,QAAA,EAEDkD,IAAI,CAACL;oBAAK;sBAAAnC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC,eACN/P,OAAA;sBACEiP,SAAS,EAAC,4CAA4C;sBACtDxB,KAAK,EAAE;wBACL7K,KAAK,EAAE,SAAS;wBAChBoO,UAAU,EAAE,6BAA6B;wBACzCH,QAAQ,EAAE;sBACZ,CAAE;sBAAA3B,QAAA,EAEDkD,IAAI,CAACJ;oBAAK;sBAAApC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC;kBAAA,GApCD9I,KAAK;oBAAA2I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAqCA,CACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,EAGZnP,OAAO,iBACNZ,OAAA,CAAC5B,MAAM,CAAC+Q,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBC,OAAO,EAAE;YAAED,OAAO,EAAE;UAAE,CAAE;UACxBJ,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAE3DlP,OAAA,CAAC5B,MAAM,CAAC+Q,GAAG;YACTG,OAAO,EAAE;cAAEC,MAAM,EAAE;YAAI,CAAE;YACzBzB,UAAU,EAAE;cAAE0B,QAAQ,EAAE,CAAC;cAAEC,MAAM,EAAEC,QAAQ;cAAEC,IAAI,EAAE;YAAS,CAAE;YAC9DV,SAAS,EAAC;UAA6E;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF,CAAC,eACF/P,OAAA;YAAGiP,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAoB;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CACb,EAGA,CAACnP,OAAO,iBACPZ,OAAA,CAAC5B,MAAM,CAAC+Q,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEe,CAAC,EAAE;UAAG,CAAE;UAC/Bd,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEe,CAAC,EAAE;UAAE,CAAE;UAC9BtC,UAAU,EAAE;YAAEwC,KAAK,EAAE,GAAG;YAAEd,QAAQ,EAAE;UAAI,CAAE;UAC1CP,SAAS,EAAC,uDAAuD;UAAAC,QAAA,eAEjElP,OAAA;YAAKiP,SAAS,EAAC,mBAAmB;YAAAC,QAAA,GAG/B3C,aAAa,CAAC1G,MAAM,GAAG,CAAC,iBACvB7F,OAAA,CAAC5B,MAAM,CAAC+Q,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEqB,KAAK,EAAE;cAAI,CAAE;cACpCpB,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEqB,KAAK,EAAE;cAAE,CAAE;cAClC5C,UAAU,EAAE;gBAAEwC,KAAK,EAAE,GAAG;gBAAEd,QAAQ,EAAE;cAAI,CAAE;cAC1CP,SAAS,EAAC,OAAO;cAAAC,QAAA,gBAEjBlP,OAAA;gBAAIiP,SAAS,EAAC,gGAAgG;gBAACxB,KAAK,EAAE;kBACpHsD,UAAU,EAAE,mDAAmD;kBAC/DY,oBAAoB,EAAE,MAAM;kBAC5BC,mBAAmB,EAAE,aAAa;kBAClCZ,UAAU,EAAE,6BAA6B;kBACzCzL,MAAM,EAAE;gBACV,CAAE;gBAAA2J,QAAA,EAAC;cAEH;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAGL/P,OAAA;gBAAKiP,SAAS,EAAC,mGAAmG;gBAAAC,QAAA,GAE/G3C,aAAa,CAAC,CAAC,CAAC,iBACfvM,OAAA,CAAC5B,MAAM,CAAC+Q,GAAG;kBAETkD,GAAG,EAAE7R,IAAI,IAAI+L,aAAa,CAAC,CAAC,CAAC,CAAC5G,GAAG,KAAKnF,IAAI,CAACmF,GAAG,GAAGrD,aAAa,GAAG,IAAK;kBACtE,gBAAciK,aAAa,CAAC,CAAC,CAAC,CAAC5G,GAAI;kBACnC,kBAAgB,CAAE;kBAClByJ,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEgB,CAAC,EAAE,CAAC,GAAG;oBAAED,CAAC,EAAE;kBAAG,CAAE;kBACxCd,OAAO,EAAE;oBACPD,OAAO,EAAE,CAAC;oBACVgB,CAAC,EAAE,CAAC;oBACJD,CAAC,EAAE,CAAC;oBACJM,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;oBACnBa,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;kBACnB,CAAE;kBACFzD,UAAU,EAAE;oBACVwC,KAAK,EAAE,GAAG;oBACVd,QAAQ,EAAE,GAAG;oBACbkB,KAAK,EAAE;sBAAElB,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY,CAAC;oBAC3D4B,OAAO,EAAE;sBAAE/B,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY;kBAC9D,CAAE;kBACFc,UAAU,EAAE;oBAAEC,KAAK,EAAE,IAAI;oBAAEN,CAAC,EAAE,CAAC;kBAAG,CAAE;kBACpCnB,SAAS,EAAG,oBAAmBzO,IAAI,IAAI+L,aAAa,CAAC,CAAC,CAAC,CAAC5G,GAAG,KAAKnF,IAAI,CAACmF,GAAG,GAAG,wBAAwB,GAAG,EAAG,IAAGnE,UAAU,IAAIhB,IAAI,IAAI+L,aAAa,CAAC,CAAC,CAAC,CAAC5G,GAAG,KAAKnF,IAAI,CAACmF,GAAG,GAAG,mBAAmB,GAAG,EAAG,EAAE;kBACjM8H,KAAK,EAAE;oBAAE6E,MAAM,EAAE;kBAAQ,CAAE;kBAAApD,QAAA,gBAG3BlP,OAAA;oBAAKiP,SAAS,EAAC,iJAAiJ;oBAAAC,QAAA,eAC9JlP,OAAA;sBAAMiP,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAG;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CAAC,eAGN/P,OAAA;oBACEiP,SAAS,EAAG,8BAA6B1C,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAAClC,KAAM,mBAAkB2J,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAAC7B,IAAK,kBAAkB;oBACpIwK,KAAK,EAAE;sBACLC,SAAS,EAAG,cAAanB,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAAC9B,WAAY,IAAG;sBAC9DuP,KAAK,EAAE;oBACT,CAAE;oBAAArD,QAAA,eAEFlP,OAAA;sBACEiP,SAAS,EAAG,GAAE1C,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAACjC,OAAQ,uEAAuE;sBAAAqM,QAAA,gBAEnHlP,OAAA;wBAAKiP,SAAS,EAAC;sBAAgE;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAGtF/P,OAAA;wBACEiP,SAAS,EAAC,oMAAoM;wBAC9MxB,KAAK,EAAE;0BACL7K,KAAK,EAAE,SAAS;0BAChB4K,MAAM,EAAE;wBACV,CAAE;wBAAA0B,QAAA,EACH;sBAED;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAGN/P,OAAA;wBAAKiP,SAAS,EAAG,yBAAwBzO,IAAI,IAAI+L,aAAa,CAAC,CAAC,CAAC,CAAC5G,GAAG,KAAKnF,IAAI,CAACmF,GAAG,GAAG,wCAAwC,GAAG,EAAG,EAAE;wBAAAuJ,QAAA,eACnIlP,OAAA;0BACEiP,SAAS,EAAC,wEAAwE;0BAClFxB,KAAK,EAAE;4BACLsD,UAAU,EAAE,SAAS;4BACrBrD,SAAS,EAAE,4BAA4B;4BACvC6E,KAAK,EAAE,MAAM;4BACbD,MAAM,EAAE;0BACV,CAAE;0BAAApD,QAAA,EAED3C,aAAa,CAAC,CAAC,CAAC,CAAClF,cAAc,gBAC9BrH,OAAA;4BACEwS,GAAG,EAAEjG,aAAa,CAAC,CAAC,CAAC,CAAClF,cAAe;4BACrCoL,GAAG,EAAElG,aAAa,CAAC,CAAC,CAAC,CAACrF,IAAK;4BAC3B+H,SAAS,EAAC;0BAAyC;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpD,CAAC,gBAEF/P,OAAA;4BACEiP,SAAS,EAAC,2EAA2E;4BACrFxB,KAAK,EAAE;8BACLsD,UAAU,EAAE,SAAS;8BACrBnO,KAAK,EAAE,SAAS;8BAChBiO,QAAQ,EAAE;4BACZ,CAAE;4BAAA3B,QAAA,EAED3C,aAAa,CAAC,CAAC,CAAC,CAACrF,IAAI,CAACwL,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;0BAAC;4BAAA/C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3C;wBACN;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAGN/P,OAAA;wBACEiP,SAAS,EAAC,iCAAiC;wBAC3CxB,KAAK,EAAE;0BAAE7K,KAAK,EAAE2J,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAAC/B;wBAAU,CAAE;wBAAAmM,QAAA,EAEjD3C,aAAa,CAAC,CAAC,CAAC,CAACrF;sBAAI;wBAAA0I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC,eAEL/P,OAAA;wBAAKiP,SAAS,EAAC,yBAAyB;wBAACxB,KAAK,EAAE;0BAAE7K,KAAK,EAAE2J,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAAChC;wBAAU,CAAE;wBAAAoM,QAAA,GACxF3C,aAAa,CAAC,CAAC,CAAC,CAAC3H,OAAO,CAACuN,cAAc,CAAC,CAAC,EAAC,KAC7C;sBAAA;wBAAAvC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAEN/P,OAAA;wBAAKiP,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,gBAChDlP,OAAA;0BAAMyN,KAAK,EAAE;4BAAE7K,KAAK,EAAE2J,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAAChC;0BAAU,CAAE;0BAAAoM,QAAA,GAAC,eACpD,EAAC3C,aAAa,CAAC,CAAC,CAAC,CAACzF,iBAAiB;wBAAA;0BAAA8I,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClC,CAAC,eACP/P,OAAA;0BAAMyN,KAAK,EAAE;4BAAE7K,KAAK,EAAE2J,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAAChC;0BAAU,CAAE;0BAAAoM,QAAA,GAAC,eACpD,EAAC3C,aAAa,CAAC,CAAC,CAAC,CAAC/E,aAAa;wBAAA;0BAAAoI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9B,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GAxGA,UAASxD,aAAa,CAAC,CAAC,CAAC,CAAC5G,GAAI,EAAC;kBAAAiK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAyG3B,CACb,EAGAxD,aAAa,CAAC,CAAC,CAAC,iBACfvM,OAAA,CAAC5B,MAAM,CAAC+Q,GAAG;kBAETkD,GAAG,EAAE7R,IAAI,IAAI+L,aAAa,CAAC,CAAC,CAAC,CAAC5G,GAAG,KAAKnF,IAAI,CAACmF,GAAG,GAAGrD,aAAa,GAAG,IAAK;kBACtE,gBAAciK,aAAa,CAAC,CAAC,CAAC,CAAC5G,GAAI;kBACnC,kBAAgB,CAAE;kBAClByJ,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEe,CAAC,EAAE,CAAC,GAAG;oBAAEM,KAAK,EAAE;kBAAI,CAAE;kBAC7CpB,OAAO,EAAE;oBACPD,OAAO,EAAE,CAAC;oBACVe,CAAC,EAAE,CAAC;oBACJM,KAAK,EAAE,CAAC;oBACRa,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;oBACxBnB,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;kBACf,CAAE;kBACFtC,UAAU,EAAE;oBACVwC,KAAK,EAAE,GAAG;oBACVd,QAAQ,EAAE,GAAG;oBACb+B,OAAO,EAAE;sBAAE/B,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY,CAAC;oBAC7DS,CAAC,EAAE;sBAAEZ,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY;kBACxD,CAAE;kBACFc,UAAU,EAAE;oBAAEC,KAAK,EAAE,IAAI;oBAAEN,CAAC,EAAE,CAAC;kBAAG,CAAE;kBACpCnB,SAAS,EAAG,yBAAwBzO,IAAI,IAAI+L,aAAa,CAAC,CAAC,CAAC,CAAC5G,GAAG,KAAKnF,IAAI,CAACmF,GAAG,GAAG,wBAAwB,GAAG,EAAG,IAAGnE,UAAU,IAAIhB,IAAI,IAAI+L,aAAa,CAAC,CAAC,CAAC,CAAC5G,GAAG,KAAKnF,IAAI,CAACmF,GAAG,GAAG,mBAAmB,GAAG,EAAG,EAAE;kBACtM8H,KAAK,EAAE;oBAAE6E,MAAM,EAAE;kBAAQ,CAAE;kBAAApD,QAAA,gBAG3BlP,OAAA;oBAAKiP,SAAS,EAAC,uJAAuJ;oBAAAC,QAAA,eACpKlP,OAAA;sBAAMiP,SAAS,EAAC,qCAAqC;sBAAAC,QAAA,EAAC;oBAAG;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eAGN/P,OAAA,CAAC5B,MAAM,CAAC+Q,GAAG;oBACTG,OAAO,EAAE;sBAAEC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;sBAAEa,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;oBAAE,CAAE;oBACpDtC,UAAU,EAAE;sBAAE0B,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC;oBAAS,CAAE;oBAC9CT,SAAS,EAAC,2DAA2D;oBAAAC,QAAA,eAErElP,OAAA,CAACtB,OAAO;sBAACuQ,SAAS,EAAC;oBAA0C;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC,eAGb/P,OAAA;oBACEiP,SAAS,EAAG,8BAA6B1C,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAAClC,KAAM,sBAAqB2J,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAAC7B,IAAK,uCAAuC;oBAC5JwK,KAAK,EAAE;sBACLC,SAAS,EAAG,cAAanB,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAAC9B,WAAY,qCAAoC;sBAC/FuP,KAAK,EAAE;oBACT,CAAE;oBAAArD,QAAA,eAEFlP,OAAA;sBACEiP,SAAS,EAAG,GAAE1C,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAACjC,OAAQ,uEAAuE;sBACnH4K,KAAK,EAAE;wBACLsD,UAAU,EAAG,GAAExE,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAACjC,OAAQ;sBAC/C,CAAE;sBAAAqM,QAAA,gBAEFlP,OAAA;wBAAKiP,SAAS,EAAC;sBAA4E;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAGlG/P,OAAA;wBACEiP,SAAS,EAAC,wMAAwM;wBAClNxB,KAAK,EAAE;0BACL7K,KAAK,EAAE,SAAS;0BAChBoO,UAAU,EAAE,6BAA6B;0BACzCxD,MAAM,EAAE;wBACV,CAAE;wBAAA0B,QAAA,EACH;sBAED;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAGN/P,OAAA;wBAAKiP,SAAS,EAAG,yBAAwBzO,IAAI,IAAI+L,aAAa,CAAC,CAAC,CAAC,CAAC5G,GAAG,KAAKnF,IAAI,CAACmF,GAAG,GAAG,wCAAwC,GAAG,EAAG,EAAE;wBAAAuJ,QAAA,gBACnIlP,OAAA;0BACEiP,SAAS,EAAC,wEAAwE;0BAClFxB,KAAK,EAAE;4BACLsD,UAAU,EAAE,SAAS;4BACrBrD,SAAS,EAAE,4BAA4B;4BACvC6E,KAAK,EAAE,MAAM;4BACbD,MAAM,EAAE;0BACV,CAAE;0BAAApD,QAAA,EAED3C,aAAa,CAAC,CAAC,CAAC,CAAClF,cAAc,gBAC9BrH,OAAA;4BACEwS,GAAG,EAAEjG,aAAa,CAAC,CAAC,CAAC,CAAClF,cAAe;4BACrCoL,GAAG,EAAElG,aAAa,CAAC,CAAC,CAAC,CAACrF,IAAK;4BAC3B+H,SAAS,EAAC;0BAAyC;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpD,CAAC,gBAEF/P,OAAA;4BACEiP,SAAS,EAAC,2EAA2E;4BACrFxB,KAAK,EAAE;8BACLsD,UAAU,EAAE,SAAS;8BACrBnO,KAAK,EAAE,SAAS;8BAChBiO,QAAQ,EAAE;4BACZ,CAAE;4BAAA3B,QAAA,EAED3C,aAAa,CAAC,CAAC,CAAC,CAACrF,IAAI,CAACwL,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;0BAAC;4BAAA/C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3C;wBACN;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,EACLvP,IAAI,IAAI+L,aAAa,CAAC,CAAC,CAAC,CAAC5G,GAAG,KAAKnF,IAAI,CAACmF,GAAG,iBACxC3F,OAAA;0BACEiP,SAAS,EAAC,4DAA4D;0BACtExB,KAAK,EAAE;4BACLsD,UAAU,EAAE,0CAA0C;4BACtDrD,SAAS,EAAE;0BACb,CAAE;0BAAAwB,QAAA,eAEFlP,OAAA,CAACrB,MAAM;4BAACsQ,SAAS,EAAC;0BAAoB;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtC,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eAGN/P,OAAA;wBACEiP,SAAS,EAAC,uCAAuC;wBACjDxB,KAAK,EAAE;0BACL7K,KAAK,EAAE2J,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAAC/B,SAAS;0BACtCiO,UAAU,EAAG,eAAczE,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAAC9B,WAAY,EAAC;0BAC9DuC,MAAM,EAAE;wBACV,CAAE;wBAAA2J,QAAA,EAED3C,aAAa,CAAC,CAAC,CAAC,CAACrF;sBAAI;wBAAA0I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC,eAEL/P,OAAA;wBACEiP,SAAS,EAAG,6DAA4D1C,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAAClC,KAAM,qDAAqD;wBACzJ6K,KAAK,EAAE;0BACLsD,UAAU,EAAG,2BAA0BxE,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAACzB,WAAY,KAAIkJ,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAAChC,SAAU,GAAE;0BAC/GF,KAAK,EAAE,SAAS;0BAChBoO,UAAU,EAAE,6BAA6B;0BACzCtD,SAAS,EAAG,cAAanB,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAAC9B,WAAY,IAAG;0BAC9DwK,MAAM,EAAE;wBACV,CAAE;wBAAA0B,QAAA,GAED3C,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAAC5B,IAAI,iBAAIlF,KAAK,CAAC4U,aAAa,CAACrG,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAAC5B,IAAI,EAAE;0BAC7E+L,SAAS,EAAE,SAAS;0BACpBxB,KAAK,EAAE;4BAAE7K,KAAK,EAAE;0BAAU;wBAC5B,CAAC,CAAC,eACF5C,OAAA;0BAAMyN,KAAK,EAAE;4BAAE7K,KAAK,EAAE;0BAAU,CAAE;0BAAAsM,QAAA,EAAE3C,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAAC3B;wBAAK;0BAAAyM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpE,CAAC,eAGN/P,OAAA;wBAAKiP,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,gBACtClP,OAAA;0BAAKiP,SAAS,EAAC,oBAAoB;0BAACxB,KAAK,EAAE;4BACzC7K,KAAK,EAAE2J,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAAC/B,SAAS;4BACtCiO,UAAU,EAAG,eAAczE,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAAC9B,WAAY,EAAC;4BAC9DuC,MAAM,EAAE;0BACV,CAAE;0BAAA2J,QAAA,GACC3C,aAAa,CAAC,CAAC,CAAC,CAAC3H,OAAO,CAACuN,cAAc,CAAC,CAAC,EAAC,KAC7C;wBAAA;0BAAAvC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAEN/P,OAAA;0BAAKiP,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,gBAChDlP,OAAA;4BAAKiP,SAAS,EAAC,aAAa;4BAAAC,QAAA,gBAC1BlP,OAAA;8BAAKiP,SAAS,EAAC,wCAAwC;8BAAAC,QAAA,gBACrDlP,OAAA,CAAClB,OAAO;gCAACmQ,SAAS,EAAC,SAAS;gCAACxB,KAAK,EAAE;kCAAE7K,KAAK,EAAE2J,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAAChC;gCAAU;8BAAE;gCAAA8M,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,eAClF/P,OAAA;gCAAMiP,SAAS,EAAC,WAAW;gCAACxB,KAAK,EAAE;kCAAE7K,KAAK,EAAE2J,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAAChC;gCAAU,CAAE;gCAAAoM,QAAA,EAC3E3C,aAAa,CAAC,CAAC,CAAC,CAACzF;8BAAiB;gCAAA8I,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC/B,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACJ,CAAC,eACN/P,OAAA;8BAAKiP,SAAS,EAAC,oBAAoB;8BAACxB,KAAK,EAAE;gCAAE7K,KAAK,EAAE2J,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAAChC;8BAAU,CAAE;8BAAAoM,QAAA,EAAC;4BAAO;8BAAAU,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjG,CAAC,eACN/P,OAAA;4BAAKiP,SAAS,EAAC,aAAa;4BAAAC,QAAA,gBAC1BlP,OAAA;8BAAKiP,SAAS,EAAC,wCAAwC;8BAAAC,QAAA,gBACrDlP,OAAA,CAACpB,OAAO;gCAACqQ,SAAS,EAAC,SAAS;gCAACxB,KAAK,EAAE;kCAAE7K,KAAK,EAAE;gCAAU;8BAAE;gCAAAgN,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,eAC5D/P,OAAA;gCAAMiP,SAAS,EAAC,WAAW;gCAACxB,KAAK,EAAE;kCAAE7K,KAAK,EAAE2J,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAAChC;gCAAU,CAAE;gCAAAoM,QAAA,EAC3E3C,aAAa,CAAC,CAAC,CAAC,CAAC/E;8BAAa;gCAAAoI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC3B,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACJ,CAAC,eACN/P,OAAA;8BAAKiP,SAAS,EAAC,oBAAoB;8BAACxB,KAAK,EAAE;gCAAE7K,KAAK,EAAE2J,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAAChC;8BAAU,CAAE;8BAAAoM,QAAA,EAAC;4BAAM;8BAAAU,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GAxKA,SAAQxD,aAAa,CAAC,CAAC,CAAC,CAAC5G,GAAI,EAAC;kBAAAiK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAyK1B,CACb,EAGAxD,aAAa,CAAC,CAAC,CAAC,iBACfvM,OAAA,CAAC5B,MAAM,CAAC+Q,GAAG;kBAETkD,GAAG,EAAE7R,IAAI,IAAI+L,aAAa,CAAC,CAAC,CAAC,CAAC5G,GAAG,KAAKnF,IAAI,CAACmF,GAAG,GAAGrD,aAAa,GAAG,IAAK;kBACtE,gBAAciK,aAAa,CAAC,CAAC,CAAC,CAAC5G,GAAI;kBACnC,kBAAgB,CAAE;kBAClByJ,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEgB,CAAC,EAAE,GAAG;oBAAED,CAAC,EAAE;kBAAG,CAAE;kBACvCd,OAAO,EAAE;oBACPD,OAAO,EAAE,CAAC;oBACVgB,CAAC,EAAE,CAAC;oBACJD,CAAC,EAAE,CAAC;oBACJM,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;oBACnBa,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;kBACpB,CAAE;kBACFzD,UAAU,EAAE;oBACVwC,KAAK,EAAE,GAAG;oBACVd,QAAQ,EAAE,GAAG;oBACbkB,KAAK,EAAE;sBAAElB,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY,CAAC;oBAC3D4B,OAAO,EAAE;sBAAE/B,QAAQ,EAAE,CAAC;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,IAAI,EAAE;oBAAY;kBAC9D,CAAE;kBACFc,UAAU,EAAE;oBAAEC,KAAK,EAAE,IAAI;oBAAEN,CAAC,EAAE,CAAC;kBAAG,CAAE;kBACpCnB,SAAS,EAAG,oBAAmBzO,IAAI,IAAI+L,aAAa,CAAC,CAAC,CAAC,CAAC5G,GAAG,KAAKnF,IAAI,CAACmF,GAAG,GAAG,wBAAwB,GAAG,EAAG,IAAGnE,UAAU,IAAIhB,IAAI,IAAI+L,aAAa,CAAC,CAAC,CAAC,CAAC5G,GAAG,KAAKnF,IAAI,CAACmF,GAAG,GAAG,mBAAmB,GAAG,EAAG,EAAE;kBACjM8H,KAAK,EAAE;oBAAE6E,MAAM,EAAE;kBAAQ,CAAE;kBAAApD,QAAA,gBAG3BlP,OAAA;oBAAKiP,SAAS,EAAC,oJAAoJ;oBAAAC,QAAA,eACjKlP,OAAA;sBAAMiP,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAG;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CAAC,eAGN/P,OAAA;oBACEiP,SAAS,EAAG,8BAA6B1C,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAAClC,KAAM,mBAAkB2J,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAAC7B,IAAK,kBAAkB;oBACpIwK,KAAK,EAAE;sBACLC,SAAS,EAAG,cAAanB,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAAC9B,WAAY,IAAG;sBAC9DuP,KAAK,EAAE;oBACT,CAAE;oBAAArD,QAAA,eAEFlP,OAAA;sBACEiP,SAAS,EAAG,GAAE1C,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAACjC,OAAQ,uEAAuE;sBAAAqM,QAAA,gBAEnHlP,OAAA;wBAAKiP,SAAS,EAAC;sBAAgE;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAGtF/P,OAAA;wBACEiP,SAAS,EAAC,sMAAsM;wBAChNxB,KAAK,EAAE;0BACL7K,KAAK,EAAE,SAAS;0BAChB4K,MAAM,EAAE;wBACV,CAAE;wBAAA0B,QAAA,EACH;sBAED;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAGN/P,OAAA;wBAAKiP,SAAS,EAAG,yBAAwBzO,IAAI,IAAI+L,aAAa,CAAC,CAAC,CAAC,CAAC5G,GAAG,KAAKnF,IAAI,CAACmF,GAAG,GAAG,wCAAwC,GAAG,EAAG,EAAE;wBAAAuJ,QAAA,eACnIlP,OAAA;0BACEiP,SAAS,EAAC,wEAAwE;0BAClFxB,KAAK,EAAE;4BACLsD,UAAU,EAAE,SAAS;4BACrBrD,SAAS,EAAE,4BAA4B;4BACvC6E,KAAK,EAAE,MAAM;4BACbD,MAAM,EAAE;0BACV,CAAE;0BAAApD,QAAA,EAED3C,aAAa,CAAC,CAAC,CAAC,CAAClF,cAAc,gBAC9BrH,OAAA;4BACEwS,GAAG,EAAEjG,aAAa,CAAC,CAAC,CAAC,CAAClF,cAAe;4BACrCoL,GAAG,EAAElG,aAAa,CAAC,CAAC,CAAC,CAACrF,IAAK;4BAC3B+H,SAAS,EAAC;0BAAyC;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpD,CAAC,gBAEF/P,OAAA;4BACEiP,SAAS,EAAC,2EAA2E;4BACrFxB,KAAK,EAAE;8BACLsD,UAAU,EAAE,SAAS;8BACrBnO,KAAK,EAAE,SAAS;8BAChBiO,QAAQ,EAAE;4BACZ,CAAE;4BAAA3B,QAAA,EAED3C,aAAa,CAAC,CAAC,CAAC,CAACrF,IAAI,CAACwL,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;0BAAC;4BAAA/C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3C;wBACN;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAGN/P,OAAA;wBACEiP,SAAS,EAAC,iCAAiC;wBAC3CxB,KAAK,EAAE;0BAAE7K,KAAK,EAAE2J,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAAC/B;wBAAU,CAAE;wBAAAmM,QAAA,EAEjD3C,aAAa,CAAC,CAAC,CAAC,CAACrF;sBAAI;wBAAA0I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC,eAEL/P,OAAA;wBAAKiP,SAAS,EAAC,yBAAyB;wBAACxB,KAAK,EAAE;0BAAE7K,KAAK,EAAE2J,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAAChC;wBAAU,CAAE;wBAAAoM,QAAA,GACxF3C,aAAa,CAAC,CAAC,CAAC,CAAC3H,OAAO,CAACuN,cAAc,CAAC,CAAC,EAAC,KAC7C;sBAAA;wBAAAvC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAEN/P,OAAA;wBAAKiP,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,gBAChDlP,OAAA;0BAAMyN,KAAK,EAAE;4BAAE7K,KAAK,EAAE2J,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAAChC;0BAAU,CAAE;0BAAAoM,QAAA,GAAC,eACpD,EAAC3C,aAAa,CAAC,CAAC,CAAC,CAACzF,iBAAiB;wBAAA;0BAAA8I,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClC,CAAC,eACP/P,OAAA;0BAAMyN,KAAK,EAAE;4BAAE7K,KAAK,EAAE2J,aAAa,CAAC,CAAC,CAAC,CAACzH,IAAI,CAAChC;0BAAU,CAAE;0BAAAoM,QAAA,GAAC,eACpD,EAAC3C,aAAa,CAAC,CAAC,CAAC,CAAC/E,aAAa;wBAAA;0BAAAoI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9B,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GAxGA,SAAQxD,aAAa,CAAC,CAAC,CAAC,CAAC5G,GAAI,EAAC;kBAAAiK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAyG1B,CACb;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EAGLrO,iBAAiB,iBAChB1B,OAAA,CAAC5B,MAAM,CAAC+Q,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEe,CAAC,EAAE;gBAAG,CAAE;gBAC/Bd,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEe,CAAC,EAAE;gBAAE,CAAE;gBAC9BtC,UAAU,EAAE;kBAAEwC,KAAK,EAAE,GAAG;kBAAEd,QAAQ,EAAE;gBAAI,CAAE;gBAC1CP,SAAS,EAAC,wJAAwJ;gBAAAC,QAAA,gBAElKlP,OAAA;kBAAKiP,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/BlP,OAAA;oBAAIiP,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,GAAC,eACnC,EAACxN,iBAAiB,CAAC0C,MAAM,CAACb,UAAU,EAAC,GAAC,EAAC7B,iBAAiB,CAAC0C,MAAM,CAACjB,KAAK;kBAAA;oBAAAyM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChF,CAAC,eACL/P,OAAA;oBAAGiP,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAC,QAC7B,EAACxN,iBAAiB,CAAC8D,QAAQ,EAAC,MAAI,EAAC9D,iBAAiB,CAACkE,aAAa,EAAC,iBACzE;kBAAA;oBAAAgK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAEN/P,OAAA;kBAAKiP,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChDlP,OAAA;oBAAKiP,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,gBACzDlP,OAAA;sBAAKiP,SAAS,EAAC,0BAA0B;sBAAAC,QAAA,EACtCxN,iBAAiB,CAAC0C,MAAM,CAACZ,WAAW,GAAG,CAAC,GACtC,GAAE9B,iBAAiB,CAAC0C,MAAM,CAACZ,WAAW,IAAI,CAAAhD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoE,OAAO,KAAI,CAAC,CAAE,KAAI,GACnE;oBAAY;sBAAAgL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEX,CAAC,eACN/P,OAAA;sBAAKiP,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAY;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC,eACN/P,OAAA;oBAAKiP,SAAS,EAAC,2CAA2C;oBAAAC,QAAA,gBACxDlP,OAAA;sBAAKiP,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,EAAExN,iBAAiB,CAACkE;oBAAa;sBAAAgK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAChF/P,OAAA;sBAAKiP,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAc;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC,eACN/P,OAAA;oBAAKiP,SAAS,EAAC,6CAA6C;oBAAAC,QAAA,gBAC1DlP,OAAA;sBAAKiP,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,GAAC,GAAC,EAACxN,iBAAiB,CAAC8D,QAAQ;oBAAA;sBAAAoK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC9E/P,OAAA;sBAAKiP,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAW;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGS,CACb,EAGA,CAACjO,cAAc,GAAGF,WAAW,GAAG4K,eAAe,EAAE3G,MAAM,GAAG,CAAC,iBAC1D7F,OAAA,CAAC5B,MAAM,CAAC+Q,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEe,CAAC,EAAE;cAAG,CAAE;cAC/Bd,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEe,CAAC,EAAE;cAAE,CAAE;cAC9BtC,UAAU,EAAE;gBAAEwC,KAAK,EAAE,CAAC;gBAAEd,QAAQ,EAAE;cAAI,CAAE;cACxCP,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBAGtClP,OAAA;gBAAKiP,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxClP,OAAA,CAAC5B,MAAM,CAACyU,EAAE;kBACR5D,SAAS,EAAC,kDAAkD;kBAC5DxB,KAAK,EAAE;oBACLsD,UAAU,EAAEjP,cAAc,GACtB,mDAAmD,GACnD,mDAAmD;oBACvD6P,oBAAoB,EAAE,MAAM;oBAC5BC,mBAAmB,EAAE,aAAa;oBAClCZ,UAAU,EAAE,6BAA6B;oBACzCzL,MAAM,EAAEzD,cAAc,GAAG,+BAA+B,GAAG;kBAC7D,CAAE;kBACFwN,OAAO,EAAE;oBAAEoB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;kBAAE,CAAE;kBACjC5C,UAAU,EAAE;oBAAE0B,QAAQ,EAAE,CAAC;oBAAEC,MAAM,EAAEC;kBAAS,CAAE;kBAAAR,QAAA,EAE7CpN,cAAc,gBACb9B,OAAA,CAAAE,SAAA;oBAAAgP,QAAA,GACGxN,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE0C,MAAM,CAACb,UAAU,EAAC,GAAC,EAAC7B,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE0C,MAAM,CAACjB,KAAK,EAAC,UAAQ,EAACzB,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE0C,MAAM,CAACb,UAAU;kBAAA,eACrH,CAAC,gBAEHvD,OAAA,CAAAE,SAAA;oBAAAgP,QAAA,EAAE;kBAAe,gBAAE;gBACpB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eACZ/P,OAAA;kBAAGiP,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,EAC1DpN,cAAc,GACV,oBAAmB,CAAAJ,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEkE,aAAa,KAAI,CAAE,UAAS,GACnE;gBAA4C;kBAAAgK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAE/C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAGN/P,OAAA;gBAAKiP,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,eACrClP,OAAA;kBAAKiP,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EACnC,CAACpN,cAAc,GAAGF,WAAW,GAAG4K,eAAe,EAAExF,GAAG,CAAC,CAAC8L,QAAQ,EAAE7L,KAAK,KAAK;oBACzE,MAAM8L,UAAU,GAAGjR,cAAc,GAC7BmF,KAAK,GAAG,CAAC,CAAC;oBAAA,EACVA,KAAK,GAAG,CAAC,CAAC,CAAC;oBACf,MAAM+L,aAAa,GAAGxS,IAAI,IAAIsS,QAAQ,CAACnN,GAAG,KAAKnF,IAAI,CAACmF,GAAG;oBAEvD,oBACE3F,OAAA,CAAC5B,MAAM,CAAC+Q,GAAG;sBAETkD,GAAG,EAAEW,aAAa,GAAGzQ,WAAW,GAAG,IAAK;sBACxC,gBAAcuQ,QAAQ,CAACnN,GAAI;sBAC3B,kBAAgBoN,UAAW;sBAC3B3D,OAAO,EAAE;wBAAEC,OAAO,EAAE,CAAC;wBAAEe,CAAC,EAAE;sBAAG,CAAE;sBAC/Bd,OAAO,EAAE;wBAAED,OAAO,EAAE,CAAC;wBAAEe,CAAC,EAAE;sBAAE,CAAE;sBAC9BtC,UAAU,EAAE;wBAAEwC,KAAK,EAAE,GAAG,GAAGrJ,KAAK,GAAG,IAAI;wBAAEuI,QAAQ,EAAE;sBAAI,CAAE;sBACzDiB,UAAU,EAAE;wBAAEC,KAAK,EAAE,IAAI;wBAAEN,CAAC,EAAE,CAAC;sBAAE,CAAE;sBACnCnB,SAAS,EAAG,+BAA8B+D,aAAa,GAAG,2BAA2B,GAAG,EAAG,IAAGxR,UAAU,IAAIwR,aAAa,GAAG,mBAAmB,GAAG,EAAG,EAAE;sBAAA9D,QAAA,eAGvJlP,OAAA;wBACEiP,SAAS,EAAG,oBAAmB6D,QAAQ,CAAChO,IAAI,CAAClC,KAAM,sBAAqBkQ,QAAQ,CAAChO,IAAI,CAAC7B,IAAK,uDAAuD;wBAClJwK,KAAK,EAAE;0BACLC,SAAS,EAAG,cAAaoF,QAAQ,CAAChO,IAAI,CAAC9B,WAAY;wBACrD,CAAE;wBAAAkM,QAAA,eAEFlP,OAAA;0BACEiP,SAAS,EAAG,GAAE6D,QAAQ,CAAChO,IAAI,CAACjC,OAAQ,oFAAoF;0BACxH4K,KAAK,EAAE;4BACLD,MAAM,EAAG,aAAYsF,QAAQ,CAAChO,IAAI,CAACzB,WAAY;0BACjD,CAAE;0BAAA6L,QAAA,gBAGFlP,OAAA;4BAAKiP,SAAS,EAAC;0BAA2E;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAGjG/P,OAAA;4BAAKiP,SAAS,EAAC,uCAAuC;4BAAAC,QAAA,gBAEpDlP,OAAA;8BAAKiP,SAAS,EAAC,UAAU;8BAAAC,QAAA,gBACvBlP,OAAA;gCACEiP,SAAS,EAAC,kJAAkJ;gCAC5JxB,KAAK,EAAE;kCACL7K,KAAK,EAAE,SAAS;kCAChBoO,UAAU,EAAE,6BAA6B;kCACzCxD,MAAM,EAAE,iCAAiC;kCACzCE,SAAS,EAAE;gCACb,CAAE;gCAAAwB,QAAA,GACH,GACE,EAAC6D,UAAU;8BAAA;gCAAAnD,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACT,CAAC,eAEN/P,OAAA;gCACEiP,SAAS,EAAC,wFAAwF;gCAClGxB,KAAK,EAAE;kCACLsD,UAAU,EAAE+B,QAAQ,CAAChO,IAAI,CAACzB,WAAW;kCACrCT,KAAK,EAAE,SAAS;kCAChBiO,QAAQ,EAAE;gCACZ,CAAE;gCAAA3B,QAAA,EAED4D,QAAQ,CAAChO,IAAI,CAAC5B,IAAI,iBAAIlD,OAAA,CAAC8S,QAAQ,CAAChO,IAAI,CAAC5B,IAAI;kCAAC+L,SAAS,EAAC;gCAAS;kCAAAW,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC9D,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC,eAGN/P,OAAA;8BAAKiP,SAAS,EAAC,UAAU;8BAAAC,QAAA,gBACvBlP,OAAA;gCACEiP,SAAS,EAAC,gEAAgE;gCAC1ExB,KAAK,EAAE;kCACLsD,UAAU,EAAE,SAAS;kCACrBrD,SAAS,EAAE,4BAA4B;kCACvC6E,KAAK,EAAE,MAAM;kCACbD,MAAM,EAAE,MAAM;kCACdW,QAAQ,EAAE,MAAM;kCAChBC,SAAS,EAAE,MAAM;kCACjBC,QAAQ,EAAE,MAAM;kCAChBC,SAAS,EAAE;gCACb,CAAE;gCAAAlE,QAAA,EAED4D,QAAQ,CAACzL,cAAc,gBACtBrH,OAAA;kCACEwS,GAAG,EAAEM,QAAQ,CAACzL,cAAe;kCAC7BoL,GAAG,EAAEK,QAAQ,CAAC5L,IAAK;kCACnB+H,SAAS,EAAC,2BAA2B;kCACrCxB,KAAK,EAAE;oCACL4F,SAAS,EAAE,OAAO;oCAClBC,cAAc,EAAE,QAAQ;oCACxBf,KAAK,EAAE,MAAM;oCACbD,MAAM,EAAE,MAAM;oCACdW,QAAQ,EAAE,MAAM;oCAChBC,SAAS,EAAE,MAAM;oCACjBC,QAAQ,EAAE,MAAM;oCAChBC,SAAS,EAAE,MAAM;oCACjBG,YAAY,EAAE;kCAChB;gCAAE;kCAAA3D,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACH,CAAC,gBAEF/P,OAAA;kCACEiP,SAAS,EAAC,6DAA6D;kCACvExB,KAAK,EAAE;oCACLsD,UAAU,EAAE,SAAS;oCACrBnO,KAAK,EAAE,SAAS;oCAChBiO,QAAQ,EAAE,MAAM;oCAChB0B,KAAK,EAAE,MAAM;oCACbD,MAAM,EAAE,MAAM;oCACdW,QAAQ,EAAE,MAAM;oCAChBC,SAAS,EAAE,MAAM;oCACjBC,QAAQ,EAAE,MAAM;oCAChBC,SAAS,EAAE,MAAM;oCACjBG,YAAY,EAAE;kCAChB,CAAE;kCAAArE,QAAA,EAED4D,QAAQ,CAAC5L,IAAI,CAACwL,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;gCAAC;kCAAA/C,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACnC;8BACN;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACE,CAAC,EAELiD,aAAa,iBACZhT,OAAA;gCACEiP,SAAS,EAAC,8FAA8F;gCACxGxB,KAAK,EAAE;kCACLsD,UAAU,EAAE,0CAA0C;kCACtDrD,SAAS,EAAE;gCACb,CAAE;gCAAAwB,QAAA,eAEFlP,OAAA,CAACrB,MAAM;kCAACsQ,SAAS,EAAC;gCAAwB;kCAAAW,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC1C,CACN;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACE,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,eAGN/P,OAAA;4BAAKiP,SAAS,EAAC,qBAAqB;4BAAAC,QAAA,eAClClP,OAAA;8BAAKiP,SAAS,EAAC,WAAW;8BAAAC,QAAA,gBAExBlP,OAAA;gCAAKiP,SAAS,EAAC,8BAA8B;gCAAAC,QAAA,gBAC3ClP,OAAA;kCACEiP,SAAS,EAAC,yCAAyC;kCACnDxB,KAAK,EAAE;oCACL7K,KAAK,EAAEkQ,QAAQ,CAAChO,IAAI,CAAC/B,SAAS;oCAC9BiO,UAAU,EAAG,eAAc8B,QAAQ,CAAChO,IAAI,CAAC9B,WAAY,EAAC;oCACtDuC,MAAM,EAAE;kCACV,CAAE;kCAAA2J,QAAA,EAED4D,QAAQ,CAAC5L;gCAAI;kCAAA0I,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACZ,CAAC,EACJiD,aAAa,iBACZhT,OAAA;kCACEiP,SAAS,EAAC,4CAA4C;kCACtDxB,KAAK,EAAE;oCACLsD,UAAU,EAAE,0CAA0C;oCACtDnO,KAAK,EAAE,SAAS;oCAChB8K,SAAS,EAAE;kCACb,CAAE;kCAAAwB,QAAA,EACH;gCAED;kCAAAU,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAM,CACP;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACE,CAAC,eAGN/P,OAAA;gCACEiP,SAAS,EAAG,4EAA2E6D,QAAQ,CAAChO,IAAI,CAAClC,KAAM,+BAA+B;gCAC1I6K,KAAK,EAAE;kCACL7K,KAAK,EAAE,SAAS;kCAChBoO,UAAU,EAAE,6BAA6B;kCACzCxD,MAAM,EAAG,aAAYsF,QAAQ,CAAChO,IAAI,CAACzB,WAAY,EAAC;kCAChDqK,SAAS,EAAG,aAAYoF,QAAQ,CAAChO,IAAI,CAAC9B,WAAY;gCACpD,CAAE;gCAAAkM,QAAA,gBAEFlP,OAAA,CAAC8S,QAAQ,CAAChO,IAAI,CAAC5B,IAAI;kCAAC+L,SAAS,EAAC;gCAAS;kCAAAW,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE,CAAC,eAC1C/P,OAAA;kCAAMiP,SAAS,EAAC,qBAAqB;kCAAAC,QAAA,EAAE4D,QAAQ,CAAChO,IAAI,CAAC3B;gCAAK;kCAAAyM,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC/D,CAAC,eAGN/P,OAAA;gCAAKiP,SAAS,EAAC,8BAA8B;gCAAAC,QAAA,GAC1C4D,QAAQ,CAACtM,KAAK,EAAC,gBAAS,EAACsM,QAAQ,CAAC1L,KAAK;8BAAA;gCAAAwI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACrC,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,eAGN/P,OAAA;4BAAKiP,SAAS,EAAC,6CAA6C;4BAAAC,QAAA,gBAE1DlP,OAAA;8BACEiP,SAAS,EAAC,oCAAoC;8BAC9CxB,KAAK,EAAE;gCACL7K,KAAK,EAAEkQ,QAAQ,CAAChO,IAAI,CAAC/B,SAAS;gCAC9BiO,UAAU,EAAG,eAAc8B,QAAQ,CAAChO,IAAI,CAAC9B,WAAY,EAAC;gCACtDuC,MAAM,EAAE;8BACV,CAAE;8BAAA2J,QAAA,GAED4D,QAAQ,CAAClO,OAAO,CAACuN,cAAc,CAAC,CAAC,EAAC,KACrC;4BAAA;8BAAAvC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eAGN/P,OAAA;8BAAKiP,SAAS,EAAC,iCAAiC;8BAAAC,QAAA,gBAC9ClP,OAAA;gCACEiP,SAAS,EAAC,8CAA8C;gCACxDxB,KAAK,EAAE;kCACLG,eAAe,EAAG,GAAEkF,QAAQ,CAAChO,IAAI,CAACzB,WAAY,IAAG;kCACjDT,KAAK,EAAEkQ,QAAQ,CAAChO,IAAI,CAAChC;gCACvB,CAAE;gCAAAoM,QAAA,gBAEFlP,OAAA,CAAClB,OAAO;kCAACmQ,SAAS,EAAC;gCAAS;kCAAAW,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE,CAAC,eAC/B/P,OAAA;kCAAMiP,SAAS,EAAC,aAAa;kCAAAC,QAAA,EAAE4D,QAAQ,CAAChM;gCAAiB;kCAAA8I,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC9D,CAAC,eACN/P,OAAA;gCACEiP,SAAS,EAAC,8CAA8C;gCACxDxB,KAAK,EAAE;kCACLG,eAAe,EAAE,WAAW;kCAC5BhL,KAAK,EAAE;gCACT,CAAE;gCAAAsM,QAAA,gBAEFlP,OAAA,CAACpB,OAAO;kCAACqQ,SAAS,EAAC;gCAAS;kCAAAW,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE,CAAC,eAC/B/P,OAAA;kCAAMiP,SAAS,EAAC,aAAa;kCAAAC,QAAA,EAAE4D,QAAQ,CAACtL;gCAAa;kCAAAoI,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC1D,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC,EAGL,CAAC,MAAM;8BACN,MAAMyD,KAAK,GAAGjF,oBAAoB,CAChCuE,QAAQ,CAACpL,kBAAkB,EAC3BoL,QAAQ,CAACtE,mBAAmB,EAC5BsE,QAAQ,CAACrE,gBAAgB,EACzBqE,QAAQ,CAACpE,eAAe,EACxBqE,UACF,CAAC;8BACD,oBACE/S,OAAA;gCACEiP,SAAS,EAAC,gFAAgF;gCAC1FxB,KAAK,EAAE;kCACLG,eAAe,EAAE4F,KAAK,CAAC3Q,OAAO;kCAC9BD,KAAK,EAAE4Q,KAAK,CAAC5Q,KAAK;kCAClB4K,MAAM,EAAG,aAAYgG,KAAK,CAACnQ,WAAY,EAAC;kCACxCwN,QAAQ,EAAE;gCACZ,CAAE;gCAAA3B,QAAA,EAEDsE,KAAK,CAACxE;8BAAI;gCAAAY,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACR,CAAC;4BAEV,CAAC,EAAE,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC,GA1OD+C,QAAQ,CAACnN,GAAG;sBAAAiK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA2OP,CAAC;kBAEjB,CAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,EAGArP,WAAW,CAACmF,MAAM,GAAG,CAAC,iBACrB7F,OAAA,CAAC5B,MAAM,CAAC+Q,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEe,CAAC,EAAE;cAAG,CAAE;cAC/Bd,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEe,CAAC,EAAE;cAAE,CAAE;cAC9BtC,UAAU,EAAE;gBAAEwC,KAAK,EAAE,GAAG;gBAAEd,QAAQ,EAAE;cAAI,CAAE;cAC1CP,SAAS,EAAC,sIAAsI;cAAAC,QAAA,eAEhJlP,OAAA;gBAAKiP,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BlP,OAAA;kBAAIiP,SAAS,EAAC,wBAAwB;kBAACxB,KAAK,EAAE;oBAC5C7K,KAAK,EAAE,SAAS;oBAChBoO,UAAU,EAAE,6BAA6B;oBACzCC,UAAU,EAAE;kBACd,CAAE;kBAAA/B,QAAA,EAAC;gBAA6B;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrC/P,OAAA;kBAAKiP,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,gBAC5DlP,OAAA;oBAAKiP,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,gBAC7ClP,OAAA;sBAAKiP,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAC9CxO,WAAW,CAAC6E,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACyC,UAAU,KAAK,SAAS,CAAC,CAACtC;oBAAM;sBAAA+J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxD,CAAC,eACN/P,OAAA;sBAAKiP,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC,eACN/P,OAAA;oBAAKiP,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5ClP,OAAA;sBAAKiP,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAC7CxO,WAAW,CAAC6E,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACyC,UAAU,KAAK,eAAe,CAAC,CAACtC;oBAAM;sBAAA+J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9D,CAAC,eACN/P,OAAA;sBAAKiP,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC,eACN/P,OAAA;oBAAKiP,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,gBAC9ClP,OAAA;sBAAKiP,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAC/CxO,WAAW,CAAC6E,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACyC,UAAU,KAAK,WAAW,CAAC,CAACtC;oBAAM;sBAAA+J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1D,CAAC,eACN/P,OAAA;sBAAKiP,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAkB;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN/P,OAAA;kBAAGiP,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAE1C;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,EAGAjP,eAAe,IAAIA,eAAe,GAAG,CAAC,iBACrCd,OAAA,CAAC5B,MAAM,CAAC+Q,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEqB,KAAK,EAAE;cAAI,CAAE;cACpCpB,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEqB,KAAK,EAAE;cAAE,CAAE;cAClC5C,UAAU,EAAE;gBAAEwC,KAAK,EAAE,GAAG;gBAAEd,QAAQ,EAAE;cAAI,CAAE;cAC1CP,SAAS,EAAC,wIAAwI;cAAAC,QAAA,eAElJlP,OAAA;gBAAKiP,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BlP,OAAA;kBAAIiP,SAAS,EAAC,yBAAyB;kBAACxB,KAAK,EAAE;oBAC7C7K,KAAK,EAAE,SAAS;oBAChBoO,UAAU,EAAE,6BAA6B;oBACzCC,UAAU,EAAE;kBACd,CAAE;kBAAA/B,QAAA,EAAC;gBAAqB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7B/P,OAAA;kBAAKiP,SAAS,EAAC,0BAA0B;kBAACxB,KAAK,EAAE;oBAC/C7K,KAAK,EAAE,SAAS;oBAChBoO,UAAU,EAAE,6BAA6B;oBACzCC,UAAU,EAAE;kBACd,CAAE;kBAAA/B,QAAA,GAAC,GAAC,EAACpO,eAAe;gBAAA;kBAAA8O,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3B/P,OAAA;kBAAGiP,SAAS,EAAC,SAAS;kBAACxB,KAAK,EAAE;oBAC5B7K,KAAK,EAAE,SAAS;oBAChBoO,UAAU,EAAE,6BAA6B;oBACzCC,UAAU,EAAE;kBACd,CAAE;kBAAA/B,QAAA,EAAC;gBAEH;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb,eAGD/P,OAAA,CAAC5B,MAAM,CAAC+Q,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEe,CAAC,EAAE;cAAG,CAAE;cAC/Bd,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEe,CAAC,EAAE;cAAE,CAAE;cAC9BtC,UAAU,EAAE;gBAAEwC,KAAK,EAAE,CAAC;gBAAEd,QAAQ,EAAE;cAAI,CAAE;cACxCP,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAE7BlP,OAAA;gBAAKiP,SAAS,EAAC,8HAA8H;gBAAAC,QAAA,gBAC3IlP,OAAA,CAAC5B,MAAM,CAAC+Q,GAAG;kBACTG,OAAO,EAAE;oBAAEoB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;kBAAE,CAAE;kBACjC5C,UAAU,EAAE;oBAAE0B,QAAQ,EAAE,CAAC;oBAAEC,MAAM,EAAEC;kBAAS,CAAE;kBAAAR,QAAA,eAE9ClP,OAAA,CAACb,QAAQ;oBAAC8P,SAAS,EAAC;kBAAwC;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACb/P,OAAA;kBAAIiP,SAAS,EAAC,yBAAyB;kBAACxB,KAAK,EAAE;oBAC7C7K,KAAK,EAAE,SAAS;oBAChBoO,UAAU,EAAE,6BAA6B;oBACzCC,UAAU,EAAE;kBACd,CAAE;kBAAA/B,QAAA,EAAC;gBAAqB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7B/P,OAAA;kBAAGiP,SAAS,EAAC,gCAAgC;kBAACxB,KAAK,EAAE;oBACnD7K,KAAK,EAAE,SAAS;oBAChBoO,UAAU,EAAE,6BAA6B;oBACzCC,UAAU,EAAE;kBACd,CAAE;kBAAA/B,QAAA,EAAC;gBAGH;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJ/P,OAAA,CAAC5B,MAAM,CAACoS,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BzB,SAAS,EAAC,sJAAsJ;kBAChK2B,OAAO,EAAEA,CAAA,KAAMzE,MAAM,CAACsH,QAAQ,CAACC,IAAI,GAAG,YAAa;kBAAAxE,QAAA,EACpD;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EAGZrP,WAAW,CAACmF,MAAM,KAAK,CAAC,IAAI,CAACjF,OAAO,iBACnCZ,OAAA,CAAC5B,MAAM,CAAC+Q,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEqB,KAAK,EAAE;cAAI,CAAE;cACpCpB,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEqB,KAAK,EAAE;cAAE,CAAE;cAClCzB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAE7BlP,OAAA,CAACvB,QAAQ;gBAACwQ,SAAS,EAAC;cAAsC;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7D/P,OAAA;gBAAIiP,SAAS,EAAC,yBAAyB;gBAACxB,KAAK,EAAE;kBAC7C7K,KAAK,EAAE,SAAS;kBAChBoO,UAAU,EAAE,6BAA6B;kBACzCC,UAAU,EAAE;gBACd,CAAE;gBAAA/B,QAAA,EAAC;cAAgB;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxB/P,OAAA;gBAAGiP,SAAS,EAAC,SAAS;gBAACxB,KAAK,EAAE;kBAC5B7K,KAAK,EAAE,SAAS;kBAChBoO,UAAU,EAAE,6BAA6B;kBACzCC,UAAU,EAAE;gBACd,CAAE;gBAAA/B,QAAA,EAAC;cAEH;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACJ,CAAC;AAEP,CAAC;AAAC3P,EAAA,CA1pEID,kBAAkB;EAAA,QACJ7B,WAAW,EAEZC,WAAW;AAAA;AAAAoV,EAAA,GAHxBxT,kBAAkB;AA4pExB,eAAeA,kBAAkB;AAAC,IAAAwT,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}